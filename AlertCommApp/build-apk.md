# Building AlertComm Android APK

## Prerequisites
1. Install Android Studio from: https://developer.android.com/studio
2. Install Android SDK (API level 34 or higher)
3. Set environment variables:
   ```bash
   export ANDROID_HOME=$HOME/Library/Android/sdk
   export PATH=$PATH:$ANDROID_HOME/emulator
   export PATH=$PATH:$ANDROID_HOME/tools
   export PATH=$PATH:$ANDROID_HOME/tools/bin
   export PATH=$PATH:$ANDROID_HOME/platform-tools
   ```

## Build Steps

### Method 1: Using Expo EAS (Cloud Build)
```bash
# Install EAS CLI
npm install -g @expo/cli

# Login to Expo (create account if needed)
npx expo login

# Build APK
npx eas build --platform android --profile preview
```

### Method 2: Local Build
```bash
# Generate Android project
npx expo prebuild --platform android

# Create local.properties file
echo "sdk.dir=$ANDROID_HOME" > android/local.properties

# Build APK
cd android
./gradlew assembleRelease
```

### Method 3: Debug APK (Faster)
```bash
cd android
./gradlew assembleDebug
```

## APK Location
After successful build, find your APK at:
- **Release**: `android/app/build/outputs/apk/release/app-release.apk`
- **Debug**: `android/app/build/outputs/apk/debug/app-debug.apk`

## Install on Device
```bash
# Enable USB debugging on your Android device
# Connect device via USB
adb install app-release.apk
```

## Troubleshooting
- Ensure Android SDK is properly installed
- Check that ANDROID_HOME is set correctly
- Make sure you have Java 11 or higher
- Verify Gradle wrapper permissions: `chmod +x gradlew`

## Current Status
- ✅ App is ready for building
- ✅ Android project files generated
- ⚠️ Requires Android SDK setup
- 📱 Use Expo Go for immediate testing
