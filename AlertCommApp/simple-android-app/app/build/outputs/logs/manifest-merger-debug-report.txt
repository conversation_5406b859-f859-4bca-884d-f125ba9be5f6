-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:2:1-32:12
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:2:1-32:12
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:2:1-32:12
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:2:1-32:12
	package
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:3:5-32
		INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml
	android:versionName
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:5:5-30
		INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:2:11-69
	android:versionCode
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:4:5-28
		INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:7:5-67
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:7:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:8:5-79
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:8:22-76
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:9:5-79
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:9:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:10:5-81
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:10:22-78
uses-permission#android.permission.CAMERA
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:11:5-65
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:11:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:12:5-71
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:12:22-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:13:5-81
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:13:22-78
uses-permission#android.permission.VIBRATE
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:14:5-66
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:14:22-63
application
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:16:5-31:19
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:16:5-31:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml
	android:label
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:19:9-53
	android:icon
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:18:9-43
	android:allowBackup
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:17:9-35
	android:theme
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:20:9-40
activity#com.alertcomm.app.MainActivity
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:22:9-30:20
	android:screenOrientation
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:25:13-49
	android:exported
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:24:13-36
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:23:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:26:13-29:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:27:17-69
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:27:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:28:17-77
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:28:27-74
uses-sdk
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml
