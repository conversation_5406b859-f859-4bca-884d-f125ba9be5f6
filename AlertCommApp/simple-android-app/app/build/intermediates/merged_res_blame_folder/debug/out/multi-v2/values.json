{"logs": [{"outputFile": "com.alertcomm.app-mergeDebugResources-2:/values/values.xml", "map": [{"source": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/res/values/strings.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}}, {"source": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/res/values/styles.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "263"}, "to": {"startLines": "3", "startColumns": "4", "startOffsets": "121", "endLines": "6", "endColumns": "12", "endOffsets": "329"}}]}]}