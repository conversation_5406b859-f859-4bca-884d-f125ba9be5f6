1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.alertcomm.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:7:5-67
11-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:7:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:8:5-79
12-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:8:22-76
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:9:5-79
13-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:9:22-76
14    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
14-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:10:5-81
14-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:10:22-78
15    <uses-permission android:name="android.permission.CAMERA" />
15-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:11:5-65
15-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:11:22-62
16    <uses-permission android:name="android.permission.RECORD_AUDIO" />
16-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:12:5-71
16-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:12:22-68
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:13:5-81
17-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:13:22-78
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:14:5-66
18-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:14:22-63
19
20    <application
20-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:16:5-31:19
21        android:allowBackup="true"
21-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:17:9-35
22        android:debuggable="true"
23        android:extractNativeLibs="false"
24        android:icon="@mipmap/ic_launcher"
24-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:18:9-43
25        android:label="AlertComm Emergency Response"
25-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:19:9-53
26        android:theme="@style/AppTheme" >
26-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:20:9-40
27        <activity
27-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:22:9-30:20
28            android:name="com.alertcomm.app.MainActivity"
28-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:23:13-41
29            android:exported="true"
29-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:24:13-36
30            android:screenOrientation="portrait" >
30-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:25:13-49
31            <intent-filter>
31-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:26:13-29:29
32                <action android:name="android.intent.action.MAIN" />
32-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:27:17-69
32-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:27:25-66
33
34                <category android:name="android.intent.category.LAUNCHER" />
34-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:28:17-77
34-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/AndroidManifest.xml:28:27-74
35            </intent-filter>
36        </activity>
37    </application>
38
39</manifest>
