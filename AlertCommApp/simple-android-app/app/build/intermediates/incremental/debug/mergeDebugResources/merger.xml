<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/res"><file path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/res/values/styles.xml" qualifiers=""><style name="AppTheme" parent="android:Theme.Material.Light.NoActionBar">
        <item name="android:statusBarColor">#1976d2</item>
        <item name="android:navigationBarColor">#1976d2</item>
    </style></file><file path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/res/values/strings.xml" qualifiers=""><string name="app_name">AlertComm Emergency Response</string></file><file name="ic_launcher" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/res/mipmap-hdpi/ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/res/mipmap-mdpi/ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/res/mipmap-xhdpi/ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/res/mipmap-xxhdpi/ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/simple-android-app/app/build/generated/res/resValues/debug"/></dataSet><mergedItems/></merger>