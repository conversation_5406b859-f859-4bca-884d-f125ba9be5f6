#!/bin/bash

echo "🚀 Creating Simple AlertComm APK"
echo "================================="

# Create simple Android project structure
mkdir -p simple-android-app/app/src/main/{java/com/alertcomm/app,assets,res/{layout,values,mipmap-hdpi,mipmap-mdpi,mipmap-xhdpi,mipmap-xxhdpi,mipmap-xxxhdpi}}

# Copy web app files to assets
echo "📋 Copying web app files..."
cp -r apk-build/* simple-android-app/app/src/main/assets/

# Create AndroidManifest.xml
cat > simple-android-app/app/src/main/AndroidManifest.xml << 'EOF'
<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.alertcomm.app"
    android:versionCode="1"
    android:versionName="1.0">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="AlertComm Emergency Response"
        android:theme="@style/AppTheme">
        
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>
</manifest>
EOF

# Create MainActivity.java
cat > simple-android-app/app/src/main/java/com/alertcomm/app/MainActivity.java << 'EOF'
package com.alertcomm.app;

import android.app.Activity;
import android.os.Bundle;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.webkit.WebSettings;

public class MainActivity extends Activity {
    private WebView webView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        webView = new WebView(this);
        setContentView(webView);
        
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowContentAccess(true);
        
        webView.setWebViewClient(new WebViewClient());
        webView.loadUrl("file:///android_asset/index.html");
    }
    
    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }
}
EOF

# Create styles.xml
cat > simple-android-app/app/src/main/res/values/styles.xml << 'EOF'
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="AppTheme" parent="android:Theme.Material.Light.NoActionBar">
        <item name="android:statusBarColor">#1976d2</item>
        <item name="android:navigationBarColor">#1976d2</item>
    </style>
</resources>
EOF

# Create strings.xml
cat > simple-android-app/app/src/main/res/values/strings.xml << 'EOF'
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">AlertComm Emergency Response</string>
</resources>
EOF

# Create build.gradle for app
cat > simple-android-app/app/build.gradle << 'EOF'
apply plugin: 'com.android.application'

android {
    compileSdkVersion 34
    defaultConfig {
        applicationId "com.alertcomm.app"
        minSdkVersion 24
        targetSdkVersion 34
        versionCode 1
        versionName "1.0"
    }
    buildTypes {
        release {
            minifyEnabled false
        }
    }
}
EOF

# Create root build.gradle
cat > simple-android-app/build.gradle << 'EOF'
buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.0'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}
EOF

# Create settings.gradle
cat > simple-android-app/settings.gradle << 'EOF'
include ':app'
EOF

# Create gradle.properties
cat > simple-android-app/gradle.properties << 'EOF'
android.useAndroidX=true
android.enableJetifier=true
EOF

# Create local.properties
cat > simple-android-app/local.properties << 'EOF'
sdk.dir=/Users/<USER>/Library/Android/sdk
EOF

echo "✅ Simple Android project created!"
echo "📱 Project location: simple-android-app/"
echo ""
echo "🔨 To build APK:"
echo "1. cd simple-android-app"
echo "2. ./gradlew assembleDebug"
echo ""
echo "📋 Or use online builders with AlertComm-WebApp.zip:"
echo "• https://www.websitetoapk.com/"
echo "• https://appsgeyser.com/"
echo "• https://gonative.io/"
echo ""
echo "🎉 Your AlertComm app is ready for APK creation!"
