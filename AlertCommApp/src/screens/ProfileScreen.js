import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Switch,
  TextInput,
  Modal,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import { useNotifications } from '../context/NotificationContext';
import apiService from '../services/api';
import socketService from '../services/socket';
import config from '../config/config';

const ProfileScreen = () => {
  const { user, logout } = useAuth();
  const { clearNotifications } = useNotifications();
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [locationEnabled, setLocationEnabled] = useState(true);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editForm, setEditForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    city: '',
    state: '',
    zip: ''
  });
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      const profileData = await apiService.getUserProfile();
      setProfile(profileData);
      // Initialize edit form with current data
      setEditForm({
        firstName: profileData.firstName || '',
        lastName: profileData.lastName || '',
        email: profileData.email || '',
        phone: profileData.phone || '',
        city: profileData.city || '',
        state: profileData.state || '',
        zip: profileData.zip || ''
      });
    } catch (error) {
      console.error('Error loading profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEditProfile = () => {
    setEditModalVisible(true);
  };

  const handleSaveProfile = async () => {
    setSaving(true);
    try {
      const updatedProfile = await apiService.updateUserProfile(editForm);
      setProfile(updatedProfile);
      setEditModalVisible(false);
      Alert.alert('Success', 'Profile updated successfully!');
    } catch (error) {
      console.error('Error updating profile:', error);
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleCancelEdit = () => {
    // Reset form to current profile data
    setEditForm({
      firstName: profile?.firstName || '',
      lastName: profile?.lastName || '',
      email: profile?.email || '',
      phone: profile?.phone || '',
      city: profile?.city || '',
      state: profile?.state || '',
      zip: profile?.zip || ''
    });
    setEditModalVisible(false);
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            socketService.disconnect();
            await clearNotifications();
            await logout();
          },
        },
      ]
    );
  };

  const renderProfileHeader = () => (
    <View style={styles.headerCard}>
      <View style={styles.avatarContainer}>
        <View style={styles.avatar}>
          <Ionicons name="person" size={40} color="#fff" />
        </View>
      </View>
      <Text style={styles.userName}>
        {profile?.firstName && profile?.lastName
          ? `${profile.firstName} ${profile.lastName}`
          : user.username
        }
      </Text>
      <Text style={styles.userRole}>{user.role?.toUpperCase() || 'STAFF'}</Text>
      {profile?.mainLocation && (
        <View style={styles.locationContainer}>
          <Ionicons name="location-outline" size={16} color={config.COLORS.textSecondary} />
          <Text style={styles.locationText}>{profile.mainLocation}</Text>
        </View>
      )}
    </View>
  );

  const renderProfileInfo = () => (
    <View style={styles.infoCard}>
      <View style={styles.cardHeader}>
        <Text style={styles.cardTitle}>Profile Information</Text>
        <TouchableOpacity style={styles.editButton} onPress={handleEditProfile}>
          <Ionicons name="pencil" size={20} color={config.COLORS.primary} />
        </TouchableOpacity>
      </View>

      <View style={styles.infoRow}>
        <Text style={styles.infoLabel}>Full Name</Text>
        <Text style={styles.infoValue}>
          {profile?.firstName && profile?.lastName
            ? `${profile.firstName} ${profile.lastName}`
            : 'Not specified'
          }
        </Text>
      </View>

      <View style={styles.infoRow}>
        <Text style={styles.infoLabel}>Email</Text>
        <Text style={styles.infoValue}>{profile?.email || 'Not specified'}</Text>
      </View>

      <View style={styles.infoRow}>
        <Text style={styles.infoLabel}>Phone</Text>
        <Text style={styles.infoValue}>{profile?.phone || 'Not specified'}</Text>
      </View>

      <View style={styles.infoRow}>
        <Text style={styles.infoLabel}>Job Role</Text>
        <Text style={styles.infoValue}>{profile?.jobRole || 'Not specified'}</Text>
      </View>

      <View style={styles.infoRow}>
        <Text style={styles.infoLabel}>Location</Text>
        <Text style={styles.infoValue}>
          {profile?.city && profile?.state
            ? `${profile.city}, ${profile.state} ${profile.zip || ''}`
            : 'Not specified'
          }
        </Text>
      </View>
    </View>
  );

  const renderSettings = () => (
    <View style={styles.settingsCard}>
      <Text style={styles.cardTitle}>Settings</Text>
      
      <View style={styles.settingRow}>
        <View style={styles.settingInfo}>
          <Ionicons name="notifications-outline" size={20} color={config.COLORS.text} />
          <View style={styles.settingText}>
            <Text style={styles.settingLabel}>Push Notifications</Text>
            <Text style={styles.settingDescription}>Receive emergency alerts</Text>
          </View>
        </View>
        <Switch
          value={notificationsEnabled}
          onValueChange={setNotificationsEnabled}
          trackColor={{ false: config.COLORS.border, true: config.COLORS.primary }}
          thumbColor={notificationsEnabled ? '#fff' : config.COLORS.disabled}
        />
      </View>
      
      <View style={styles.settingRow}>
        <View style={styles.settingInfo}>
          <Ionicons name="location-outline" size={20} color={config.COLORS.text} />
          <View style={styles.settingText}>
            <Text style={styles.settingLabel}>Location Services</Text>
            <Text style={styles.settingDescription}>Share location with team</Text>
          </View>
        </View>
        <Switch
          value={locationEnabled}
          onValueChange={setLocationEnabled}
          trackColor={{ false: config.COLORS.border, true: config.COLORS.primary }}
          thumbColor={locationEnabled ? '#fff' : config.COLORS.disabled}
        />
      </View>
    </View>
  );

  const renderActions = () => (
    <View style={styles.actionsCard}>
      <TouchableOpacity
        style={styles.actionButton}
        onPress={() => navigation.navigate('ResponderNotifications')}
      >
        <Ionicons name="notifications-outline" size={20} color={config.COLORS.text} />
        <Text style={styles.actionText}>Event Assignments</Text>
        <Ionicons name="chevron-forward" size={16} color={config.COLORS.textSecondary} />
      </TouchableOpacity>

      <TouchableOpacity style={styles.actionButton}>
        <Ionicons name="help-circle-outline" size={20} color={config.COLORS.text} />
        <Text style={styles.actionText}>Help & Support</Text>
        <Ionicons name="chevron-forward" size={16} color={config.COLORS.textSecondary} />
      </TouchableOpacity>
      
      <TouchableOpacity style={styles.actionButton}>
        <Ionicons name="information-circle-outline" size={20} color={config.COLORS.text} />
        <Text style={styles.actionText}>About</Text>
        <Ionicons name="chevron-forward" size={16} color={config.COLORS.textSecondary} />
      </TouchableOpacity>
      
      <TouchableOpacity style={styles.actionButton}>
        <Ionicons name="document-text-outline" size={20} color={config.COLORS.text} />
        <Text style={styles.actionText}>Privacy Policy</Text>
        <Ionicons name="chevron-forward" size={16} color={config.COLORS.textSecondary} />
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={[styles.actionButton, styles.logoutButton]}
        onPress={handleLogout}
      >
        <Ionicons name="log-out-outline" size={20} color={config.COLORS.error} />
        <Text style={[styles.actionText, styles.logoutText]}>Logout</Text>
        <Ionicons name="chevron-forward" size={16} color={config.COLORS.error} />
      </TouchableOpacity>
    </View>
  );

  const renderAppInfo = () => (
    <View style={styles.appInfoCard}>
      <Text style={styles.appName}>AlertComm Emergency Response</Text>
      <Text style={styles.appVersion}>Version {config.APP_VERSION}</Text>
      <Text style={styles.appDescription}>
        Professional emergency response coordination system
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading profile...</Text>
      </View>
    );
  }

  return (
    <>
      <ScrollView style={styles.container}>
        {renderProfileHeader()}
        {renderProfileInfo()}
        {renderSettings()}
        {renderActions()}
        {renderAppInfo()}
      </ScrollView>

      {/* Edit Profile Modal */}
      <Modal
        visible={editModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <KeyboardAvoidingView
          style={styles.modalContainer}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={handleCancelEdit}>
              <Text style={styles.modalCancelButton}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Edit Profile</Text>
            <TouchableOpacity onPress={handleSaveProfile} disabled={saving}>
              <Text style={[styles.modalSaveButton, saving && styles.disabledButton]}>
                {saving ? 'Saving...' : 'Save'}
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>First Name</Text>
              <TextInput
                style={styles.textInput}
                value={editForm.firstName}
                onChangeText={(text) => setEditForm({...editForm, firstName: text})}
                placeholder="Enter first name"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Last Name</Text>
              <TextInput
                style={styles.textInput}
                value={editForm.lastName}
                onChangeText={(text) => setEditForm({...editForm, lastName: text})}
                placeholder="Enter last name"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Email</Text>
              <TextInput
                style={styles.textInput}
                value={editForm.email}
                onChangeText={(text) => setEditForm({...editForm, email: text})}
                placeholder="Enter email address"
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Phone</Text>
              <TextInput
                style={styles.textInput}
                value={editForm.phone}
                onChangeText={(text) => setEditForm({...editForm, phone: text})}
                placeholder="Enter phone number"
                keyboardType="phone-pad"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>City</Text>
              <TextInput
                style={styles.textInput}
                value={editForm.city}
                onChangeText={(text) => setEditForm({...editForm, city: text})}
                placeholder="Enter city"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>State</Text>
              <TextInput
                style={styles.textInput}
                value={editForm.state}
                onChangeText={(text) => setEditForm({...editForm, state: text})}
                placeholder="Enter state"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>ZIP Code</Text>
              <TextInput
                style={styles.textInput}
                value={editForm.zip}
                onChangeText={(text) => setEditForm({...editForm, zip: text})}
                placeholder="Enter ZIP code"
                keyboardType="numeric"
              />
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: config.COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerCard: {
    backgroundColor: config.COLORS.surface,
    alignItems: 'center',
    padding: 24,
    marginBottom: 16,
  },
  avatarContainer: {
    marginBottom: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: config.COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: config.COLORS.text,
    marginBottom: 4,
  },
  userRole: {
    fontSize: 14,
    color: config.COLORS.primary,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    marginLeft: 4,
  },
  infoCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: config.COLORS.text,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
  },
  infoLabel: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    flex: 1,
  },
  infoValue: {
    fontSize: 14,
    color: config.COLORS.text,
    flex: 2,
    textAlign: 'right',
  },
  settingsCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingText: {
    marginLeft: 12,
    flex: 1,
  },
  settingLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: config.COLORS.text,
  },
  settingDescription: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
    marginTop: 2,
  },
  actionsCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    marginTop: 0,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
  },
  actionText: {
    fontSize: 14,
    color: config.COLORS.text,
    marginLeft: 12,
    flex: 1,
  },
  logoutButton: {
    borderBottomWidth: 0,
  },
  logoutText: {
    color: config.COLORS.error,
  },
  appInfoCard: {
    alignItems: 'center',
    padding: 24,
    margin: 16,
    marginTop: 0,
  },
  appName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: config.COLORS.text,
    marginBottom: 4,
  },
  appVersion: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
    marginBottom: 8,
  },
  appDescription: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
    textAlign: 'center',
  },
  // Edit Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: config.COLORS.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
    backgroundColor: config.COLORS.surface,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: config.COLORS.text,
  },
  modalCancelButton: {
    fontSize: 16,
    color: config.COLORS.textSecondary,
  },
  modalSaveButton: {
    fontSize: 16,
    color: config.COLORS.primary,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.5,
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: config.COLORS.text,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: config.COLORS.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: config.COLORS.surface,
    color: config.COLORS.text,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  editButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: config.COLORS.primary + '20',
  },
});

export default ProfileScreen;
