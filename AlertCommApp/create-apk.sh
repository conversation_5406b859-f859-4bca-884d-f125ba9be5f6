#!/bin/bash

echo "🚀 Creating AlertComm APK for Android Testing"
echo "=============================================="

# Method 1: Create a simple APK using Expo's web export + Capacitor
echo "📱 Method 1: Creating Web-based APK..."

# Install Capacitor if not installed
if ! command -v cap &> /dev/null; then
    echo "📦 Installing Capacitor..."
    npm install -g @capacitor/cli @capacitor/core @capacitor/android
fi

# Initialize Capacitor
echo "🔧 Setting up Capacitor..."
npx cap init "AlertComm Emergency Response" "com.alertcomm.app" --web-dir=dist

# Add Android platform
echo "📱 Adding Android platform..."
npx cap add android

# Copy web assets
echo "📋 Copying web build to Android..."
npx cap copy android

# Build APK
echo "🔨 Building APK..."
cd android
./gradlew assembleDebug

# Check if APK was created
APK_PATH="app/build/outputs/apk/debug/app-debug.apk"
if [ -f "$APK_PATH" ]; then
    echo "✅ APK created successfully!"
    echo "📱 APK location: android/$APK_PATH"
    
    # Copy to root for easy access
    cp "$APK_PATH" "../AlertComm-debug.apk"
    echo "📱 APK copied to: AlertComm-debug.apk"
    
    # Show file info
    ls -lh "../AlertComm-debug.apk"
    
    echo ""
    echo "🎉 SUCCESS! Your AlertComm APK is ready!"
    echo "📱 Install AlertComm-debug.apk on your Android device"
    echo ""
    echo "To install:"
    echo "1. Enable 'Unknown Sources' in Android Settings"
    echo "2. Transfer AlertComm-debug.apk to your device"
    echo "3. Tap the APK file to install"
    
else
    echo "❌ APK build failed!"
    echo ""
    echo "🔄 Trying alternative method..."
    
    # Method 2: Use Expo's simple export
    echo "📱 Method 2: Creating Expo-based APK..."
    cd ..
    
    # Create a simple HTML wrapper
    mkdir -p apk-build
    cp -r dist/* apk-build/
    
    echo "📋 APK build files created in apk-build/ directory"
    echo "📱 You can use these files with Android Studio or online APK builders"
    echo ""
    echo "Online APK builders you can use:"
    echo "1. https://www.websitetoapk.com/"
    echo "2. https://appsgeyser.com/"
    echo "3. https://gonative.io/"
    echo ""
    echo "Upload the contents of apk-build/ directory to create your APK"
fi

echo ""
echo "🚀 Build process complete!"
