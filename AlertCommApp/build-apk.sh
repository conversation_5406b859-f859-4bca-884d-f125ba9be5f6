#!/bin/bash

echo "🚀 Building AlertComm APK for Android..."

# Set Android environment variables
export ANDROID_HOME=/Users/<USER>/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/platform-tools:$ANDROID_HOME/tools:$ANDROID_HOME/build-tools/35.0.0

echo "📱 Android SDK Path: $ANDROID_HOME"

# Check if Android SDK exists
if [ ! -d "$ANDROID_HOME" ]; then
    echo "❌ Android SDK not found at $ANDROID_HOME"
    echo "Please install Android Studio and set up Android SDK"
    exit 1
fi

echo "✅ Android SDK found"

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf android/app/build/outputs/apk/

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Generate Android project files
echo "🔧 Generating Android project..."
npx expo prebuild --platform android --clear

# Build the APK
echo "🔨 Building APK..."
cd android
./gradlew assembleDebug

# Check if APK was created
APK_PATH="app/build/outputs/apk/debug/app-debug.apk"
if [ -f "$APK_PATH" ]; then
    echo "✅ APK built successfully!"
    echo "📱 APK location: android/$APK_PATH"
    
    # Copy APK to root directory for easy access
    cp "$APK_PATH" "../AlertComm-debug.apk"
    echo "📱 APK copied to: AlertComm-debug.apk"
    
    # Show APK info
    echo "📊 APK Information:"
    ls -lh "../AlertComm-debug.apk"
    
else
    echo "❌ APK build failed!"
    echo "Check the build logs above for errors"
    exit 1
fi

echo "🎉 Build complete! Install AlertComm-debug.apk on your Android device."
