{"root": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp", "reactNativePath": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native", "dependencies": {"@react-native-async-storage/async-storage": {"root": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage", "name": "@react-native-async-storage/async-storage", "platforms": {"android": {"sourceDir": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android", "packageImportPath": "import com.reactnativecommunity.asyncstorage.AsyncStoragePackage;", "packageInstance": "new AsyncStoragePackage()", "buildTypes": [], "libraryName": "rnasyncstorage", "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-picker/picker": {"root": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker", "name": "@react-native-picker/picker", "platforms": {"android": {"sourceDir": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android", "packageImportPath": "import com.reactnativecommunity.picker.RNCPickerPackage;", "packageInstance": "new RNCPickerPackage()", "buildTypes": [], "libraryName": "rnpicker", "componentDescriptors": ["RNCAndroidDialogPickerComponentDescriptor", "RNCAndroidDropdownPickerComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "expo": {"root": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo", "name": "expo", "platforms": {"android": {"sourceDir": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo/android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-gesture-handler": {"root": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler", "name": "react-native-gesture-handler", "platforms": {"android": {"sourceDir": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android", "packageImportPath": "import com.swmansion.gesturehandler.RNGestureHandlerPackage;", "packageInstance": "new RNGestureHandlerPackage()", "buildTypes": [], "libraryName": "rngesturehandler_codegen", "componentDescriptors": ["RNGestureHandlerRootViewComponentDescriptor", "RNGestureHandlerButtonComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-maps": {"root": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps", "name": "react-native-maps", "platforms": {"android": {"sourceDir": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android", "packageImportPath": "import com.rnmaps.maps.MapsPackage;", "packageInstance": "new MapsPackage()", "buildTypes": [], "libraryName": "RNMapsSpecs", "componentDescriptors": ["RNMapsWMSTileComponentDescriptor", "RNMapsUrlTileComponentDescriptor", "RNMapsPolylineComponentDescriptor", "RNMapsOverlayComponentDescriptor", "RNMapsMarkerComponentDescriptor", "RNMapsMapViewComponentDescriptor", "RNMapsGooglePolygonComponentDescriptor", "RNMapsGoogleMapViewComponentDescriptor", "RNMapsCircleComponentDescriptor", "RNMapsCalloutComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-push-notification": {"root": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-push-notification", "name": "react-native-push-notification", "platforms": {"android": {"sourceDir": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-push-notification/android", "packageImportPath": "import com.dieam.reactnativepushnotification.ReactNativePushNotificationPackage;", "packageInstance": "new ReactNativePushNotificationPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-push-notification/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-safe-area-context": {"root": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-screens": {"root": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-vector-icons": {"root": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons", "name": "react-native-vector-icons", "platforms": {"android": {"sourceDir": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android", "packageImportPath": "import com.oblador.vectoricons.VectorIconsPackage;", "packageInstance": "new VectorIconsPackage()", "buildTypes": [], "libraryName": "RNVectorIconsSpec", "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-edge-to-edge": {"root": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge", "name": "react-native-edge-to-edge", "platforms": {"android": {"sourceDir": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android", "packageImportPath": "import com.zoontek.rnedgetoedge.EdgeToEdgePackage;", "packageInstance": "new EdgeToEdgePackage()", "buildTypes": [], "libraryName": "RNEdgeToEdge", "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "com.alertcomm.app", "sourceDir": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android"}}}