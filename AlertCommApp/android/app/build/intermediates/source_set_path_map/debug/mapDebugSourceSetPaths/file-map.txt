com.alertcomm.app-profileinstaller-1.3.1-0 /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/res
com.alertcomm.app-activity-ktx-1.8.0-1 /Users/<USER>/.gradle/caches/8.13/transforms/0778a2aa5b019b2def3eb71c0ed4a154/transformed/activity-ktx-1.8.0/res
com.alertcomm.app-lifecycle-runtime-2.6.2-2 /Users/<USER>/.gradle/caches/8.13/transforms/09f7c3694aac112487e9f730f6c374d4/transformed/lifecycle-runtime-2.6.2/res
com.alertcomm.app-lifecycle-viewmodel-savedstate-2.6.2-3 /Users/<USER>/.gradle/caches/8.13/transforms/0a40c827f1ab6cae60b0a38160d029dc/transformed/lifecycle-viewmodel-savedstate-2.6.2/res
com.alertcomm.app-appcompat-v7-27.1.1-4 /Users/<USER>/.gradle/caches/8.13/transforms/0fb6aadda9e6da50ceeb51b26b8280d9/transformed/appcompat-v7-27.1.1/res
com.alertcomm.app-drawerlayout-1.1.1-5 /Users/<USER>/.gradle/caches/8.13/transforms/183e370a152c2b8981bc4a82353880f3/transformed/drawerlayout-1.1.1/res
com.alertcomm.app-tracing-ktx-1.2.0-6 /Users/<USER>/.gradle/caches/8.13/transforms/18cdabc45926f1dc3dff94376b7201d0/transformed/tracing-ktx-1.2.0/res
com.alertcomm.app-constraintlayout-2.0.1-7 /Users/<USER>/.gradle/caches/8.13/transforms/1b766223c9ad7f2daee8b81f58bc7213/transformed/constraintlayout-2.0.1/res
com.alertcomm.app-startup-runtime-1.1.1-8 /Users/<USER>/.gradle/caches/8.13/transforms/1c233a1110e41e54547ee5b84513eb96/transformed/startup-runtime-1.1.1/res
com.alertcomm.app-core-runtime-2.2.0-9 /Users/<USER>/.gradle/caches/8.13/transforms/274a5e4c52765583c47c315e8a011727/transformed/core-runtime-2.2.0/res
com.alertcomm.app-fragment-1.6.1-10 /Users/<USER>/.gradle/caches/8.13/transforms/27ed71059cba4dfa5c0c363f337b269f/transformed/fragment-1.6.1/res
com.alertcomm.app-coordinatorlayout-1.2.0-11 /Users/<USER>/.gradle/caches/8.13/transforms/28bc247258e5cff6e6aac54078e1de43/transformed/coordinatorlayout-1.2.0/res
com.alertcomm.app-cardview-1.0.0-12 /Users/<USER>/.gradle/caches/8.13/transforms/2ba03b814d30daf0b666892538f8bffe/transformed/cardview-1.0.0/res
com.alertcomm.app-expo.modules.notifications-0.31.4-13 /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/res
com.alertcomm.app-support-core-ui-27.1.1-14 /Users/<USER>/.gradle/caches/8.13/transforms/3b3f9e97332bb6a6eee18f166ea1f611/transformed/support-core-ui-27.1.1/res
com.alertcomm.app-appcompat-1.7.0-15 /Users/<USER>/.gradle/caches/8.13/transforms/4bf03df1e227318d4ab7c4ce04613cbc/transformed/appcompat-1.7.0/res
com.alertcomm.app-emoji2-views-helper-1.3.0-16 /Users/<USER>/.gradle/caches/8.13/transforms/51166ce65ea68e12719e2bca48ab2cfd/transformed/emoji2-views-helper-1.3.0/res
com.alertcomm.app-lifecycle-viewmodel-2.6.2-17 /Users/<USER>/.gradle/caches/8.13/transforms/58726736d2c8b2cb18170698e2d2af0c/transformed/lifecycle-viewmodel-2.6.2/res
com.alertcomm.app-play-services-maps-19.1.0-18 /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/res
com.alertcomm.app-recyclerview-1.1.0-19 /Users/<USER>/.gradle/caches/8.13/transforms/59364def5c2f903fdc1dceeb75244942/transformed/recyclerview-1.1.0/res
com.alertcomm.app-room-runtime-2.5.0-20 /Users/<USER>/.gradle/caches/8.13/transforms/61c957488e5abc44912a78441e6969b6/transformed/room-runtime-2.5.0/res
com.alertcomm.app-support-compat-27.1.1-21 /Users/<USER>/.gradle/caches/8.13/transforms/638f9dafce3929bc2858ca351b53354e/transformed/support-compat-27.1.1/res
com.alertcomm.app-play-services-basement-18.4.0-22 /Users/<USER>/.gradle/caches/8.13/transforms/667e71e4345aed7ed3545c710439fc52/transformed/play-services-basement-18.4.0/res
com.alertcomm.app-emoji2-1.3.0-23 /Users/<USER>/.gradle/caches/8.13/transforms/6a06dd8db20fe48f1d4dcd15ee18fe12/transformed/emoji2-1.3.0/res
com.alertcomm.app-viewpager2-1.0.0-24 /Users/<USER>/.gradle/caches/8.13/transforms/6bc00ab5eaca23c3c3ebd49ed8511ff9/transformed/viewpager2-1.0.0/res
com.alertcomm.app-drawee-3.6.0-25 /Users/<USER>/.gradle/caches/8.13/transforms/6fcecc4c6bb8b8753bdcf3c3459c4fbc/transformed/drawee-3.6.0/res
com.alertcomm.app-core-ktx-1.15.0-26 /Users/<USER>/.gradle/caches/8.13/transforms/72a94180d096fd79574b78e6407bc370/transformed/core-ktx-1.15.0/res
com.alertcomm.app-material-1.12.0-27 /Users/<USER>/.gradle/caches/8.13/transforms/7a9086f53045ff51dd925c6f1785f9fd/transformed/material-1.12.0/res
com.alertcomm.app-lifecycle-livedata-core-2.6.2-28 /Users/<USER>/.gradle/caches/8.13/transforms/7c99fc93f13e5345aee29eaa313ee725/transformed/lifecycle-livedata-core-2.6.2/res
com.alertcomm.app-sqlite-framework-2.3.0-29 /Users/<USER>/.gradle/caches/8.13/transforms/7d1e55601ba19bcfcdbfa4a02130ac50/transformed/sqlite-framework-2.3.0/res
com.alertcomm.app-lifecycle-livedata-core-ktx-2.6.2-30 /Users/<USER>/.gradle/caches/8.13/transforms/7d4851cc4faabf79f6af50acd560b500/transformed/lifecycle-livedata-core-ktx-2.6.2/res
com.alertcomm.app-core-1.15.0-31 /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/res
com.alertcomm.app-savedstate-ktx-1.2.1-32 /Users/<USER>/.gradle/caches/8.13/transforms/829cfe53c64cfc693496f8451f29e7a8/transformed/savedstate-ktx-1.2.1/res
com.alertcomm.app-lifecycle-viewmodel-ktx-2.6.2-33 /Users/<USER>/.gradle/caches/8.13/transforms/850320aa2b4b52ced6f3465503644aaa/transformed/lifecycle-viewmodel-ktx-2.6.2/res
com.alertcomm.app-lifecycle-livedata-2.6.2-34 /Users/<USER>/.gradle/caches/8.13/transforms/86cd36f2af6251908c1cd91569dccf65/transformed/lifecycle-livedata-2.6.2/res
com.alertcomm.app-android-maps-utils-3.10.0-35 /Users/<USER>/.gradle/caches/8.13/transforms/8a27fe877e7ae68239dd68f638887df5/transformed/android-maps-utils-3.10.0/res
com.alertcomm.app-firebase-messaging-24.0.1-36 /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/res
com.alertcomm.app-expo.modules.filesystem-18.1.11-37 /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/res
com.alertcomm.app-sqlite-2.3.0-38 /Users/<USER>/.gradle/caches/8.13/transforms/9b4397416aa634d6c66b07fe5b0df812/transformed/sqlite-2.3.0/res
com.alertcomm.app-swiperefreshlayout-1.1.0-39 /Users/<USER>/.gradle/caches/8.13/transforms/a177d82bc21b186c301bc9b6c8d7387d/transformed/swiperefreshlayout-1.1.0/res
com.alertcomm.app-activity-1.8.0-40 /Users/<USER>/.gradle/caches/8.13/transforms/a4309c375f7c655b9a04946660dc90cc/transformed/activity-1.8.0/res
com.alertcomm.app-lifecycle-service-2.6.2-41 /Users/<USER>/.gradle/caches/8.13/transforms/b153c5f07cea60c340fe199e7ef9098d/transformed/lifecycle-service-2.6.2/res
com.alertcomm.app-firebase-common-21.0.0-42 /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/res
com.alertcomm.app-autofill-1.1.0-43 /Users/<USER>/.gradle/caches/8.13/transforms/b52e52fc777190b4295f59d0a858517f/transformed/autofill-1.1.0/res
com.alertcomm.app-lifecycle-process-2.6.2-44 /Users/<USER>/.gradle/caches/8.13/transforms/b59c4c4f9e32b429eb79d019301b76c8/transformed/lifecycle-process-2.6.2/res
com.alertcomm.app-fragment-ktx-1.6.1-45 /Users/<USER>/.gradle/caches/8.13/transforms/bd599e307399f0c0086eaf5c5077a9e1/transformed/fragment-ktx-1.6.1/res
com.alertcomm.app-transition-1.5.0-46 /Users/<USER>/.gradle/caches/8.13/transforms/c2aaf2f0c5250c0e148ab60ada39c9da/transformed/transition-1.5.0/res
com.alertcomm.app-annotation-experimental-1.4.1-47 /Users/<USER>/.gradle/caches/8.13/transforms/c72240e70788902de7eb47bfe9d21a7b/transformed/annotation-experimental-1.4.1/res
com.alertcomm.app-work-runtime-2.9.1-48 /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/res
com.alertcomm.app-play-services-base-18.5.0-49 /Users/<USER>/.gradle/caches/8.13/transforms/ca5f3ad87bb5a176fcf5402bbea57c24/transformed/play-services-base-18.5.0/res
com.alertcomm.app-appcompat-resources-1.7.0-50 /Users/<USER>/.gradle/caches/8.13/transforms/cafe38d429a8d27ed8b0af5abbc941f2/transformed/appcompat-resources-1.7.0/res
com.alertcomm.app-react-android-0.79.5-debug-51 /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/res
com.alertcomm.app-lifecycle-runtime-ktx-2.6.2-52 /Users/<USER>/.gradle/caches/8.13/transforms/de145cd6b45e8b1fb7e804d6c000ef76/transformed/lifecycle-runtime-ktx-2.6.2/res
com.alertcomm.app-browser-1.6.0-53 /Users/<USER>/.gradle/caches/8.13/transforms/dfa8d219fb756bbf0447e8d2f088c459/transformed/browser-1.6.0/res
com.alertcomm.app-media-1.0.0-54 /Users/<USER>/.gradle/caches/8.13/transforms/e2740c2d15f5f0e1c1cc73d5e9e0b8e0/transformed/media-1.0.0/res
com.alertcomm.app-room-ktx-2.5.0-55 /Users/<USER>/.gradle/caches/8.13/transforms/e82a40cf8f8f2afb94477070470e0b58/transformed/room-ktx-2.5.0/res
com.alertcomm.app-tracing-1.2.0-56 /Users/<USER>/.gradle/caches/8.13/transforms/f7ab8f7120f36931c3468a226fbeffc5/transformed/tracing-1.2.0/res
com.alertcomm.app-savedstate-1.2.1-57 /Users/<USER>/.gradle/caches/8.13/transforms/fcc1cc582935f0fa4fa7ad13b3b8686c/transformed/savedstate-1.2.1/res
com.alertcomm.app-pngs-58 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/res/pngs/debug
com.alertcomm.app-resValues-59 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/res/resValues/debug
com.alertcomm.app-packageDebugResources-60 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/incremental/debug/packageDebugResources/merged.dir
com.alertcomm.app-packageDebugResources-61 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/incremental/debug/packageDebugResources/stripped.dir
com.alertcomm.app-debug-62 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/merged_res/debug/mergeDebugResources
com.alertcomm.app-debug-63 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/res
com.alertcomm.app-main-64 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/res
com.alertcomm.app-debug-65 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-66 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-67 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-constants/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-68 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-client/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-69 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-70 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu-interface/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-71 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-72 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-json-utils/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-73 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-manifests/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-74 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-75 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-permissions/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-76 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-updates-interface/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-77 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-78 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-79 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-80 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-81 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-push-notification/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-82 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-83 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/intermediates/packaged_res/debug/packageDebugResources
com.alertcomm.app-debug-84 /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/intermediates/packaged_res/debug/packageDebugResources
