1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.alertcomm.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:6:3-75
11-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:6:20-73
12    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
12-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:2:3-78
12-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:2:20-76
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:3:3-76
13-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:3:20-74
14    <uses-permission android:name="android.permission.INTERNET" />
14-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:4:3-64
14-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:4:20-62
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:5:3-77
15-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:5:20-75
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:7:3-63
16-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:7:20-61
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:8:3-78
17-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:8:20-76
18
19    <queries>
19-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:9:3-15:13
20        <intent>
20-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:10:5-14:14
21            <action android:name="android.intent.action.VIEW" />
21-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:11:7-58
21-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:11:15-56
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:12:7-67
23-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:12:17-65
24
25            <data android:scheme="https" />
25-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:13:7-37
25-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:13:13-35
26        </intent>
27
28        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
28-->[:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-53
28-->[:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:18-50
29        <intent>
29-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:15:9-17:18
30            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
30-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:16:13-79
30-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:16:21-76
31        </intent> <!-- Needs to be explicitly declared on Android R+ -->
32        <package android:name="com.google.android.apps.maps" />
32-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:29:7-61
32-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:29:16-59
33    </queries>
34
35    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
35-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:7:5-81
35-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:7:22-78
36    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
36-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:8:5-77
36-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:8:22-74
37    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
37-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:22:5-79
37-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:22:22-76
38    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
38-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:24:5-68
38-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:24:22-65
39    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
39-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:26:5-82
39-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:26:22-79
40    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
40-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:26:5-77
40-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:26:22-74
41
42    <uses-feature
42-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:22:5-24:33
43        android:glEsVersion="0x00020000"
43-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:23:8-40
44        android:required="true" />
44-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:24:8-31
45
46    <permission
46-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:22:5-24:47
47        android:name="com.alertcomm.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
47-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:23:9-81
48        android:protectionLevel="signature" />
48-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:24:9-44
49
50    <uses-permission android:name="com.alertcomm.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- for android -->
50-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:26:5-97
50-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:26:22-94
51    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
52    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
53    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
54    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
55    <!-- for Samsung -->
56    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
56-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:5-86
56-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:22-83
57    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
57-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:5-87
57-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:22-84
58    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
58-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:5-81
58-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:22-78
59    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
59-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:5-83
59-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:22-80
60    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
60-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:5-88
60-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:22-85
61    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
61-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:5-92
61-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:22-89
62    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
62-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:5-84
62-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:22-81
63    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
63-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:5-83
63-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:22-80
64    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
64-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:5-91
64-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:22-88
65    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
65-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:5-92
65-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:22-89
66    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
66-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:5-93
66-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:22-90
67    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
67-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:5-73
67-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:22-70
68    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
68-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:5-82
68-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:22-79
69    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
69-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:5-83
69-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:22-80
70    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
70-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:5-88
70-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:22-85
71    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
71-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:5-89
71-->[me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:22-86
72    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
72-->[com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/c606388d75cd1d2425bd93631a9c5065/transformed/installreferrer-2.2/AndroidManifest.xml:9:5-110
72-->[com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/c606388d75cd1d2425bd93631a9c5065/transformed/installreferrer-2.2/AndroidManifest.xml:9:22-107
73
74    <application
74-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:3-36:17
75        android:name="com.alertcomm.app.MainApplication"
75-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:16-47
76        android:allowBackup="true"
76-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:162-188
77        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
77-->[androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:28:18-86
78        android:debuggable="true"
79        android:extractNativeLibs="false"
80        android:icon="@mipmap/ic_launcher"
80-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:81-115
81        android:label="@string/app_name"
81-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:48-80
82        android:roundIcon="@mipmap/ic_launcher_round"
82-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:116-161
83        android:supportsRtl="true"
83-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:221-247
84        android:theme="@style/AppTheme"
84-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:189-220
85        android:usesCleartextTraffic="true" >
85-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:6:18-53
86        <meta-data
86-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:17:5-139
87            android:name="com.google.firebase.messaging.default_notification_color"
87-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:17:16-87
88            android:resource="@color/notification_icon_color" />
88-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:17:88-137
89        <meta-data
89-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:18:5-135
90            android:name="com.google.firebase.messaging.default_notification_icon"
90-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:18:16-86
91            android:resource="@drawable/notification_icon" />
91-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:18:87-133
92        <meta-data
92-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:19:5-136
93            android:name="expo.modules.notifications.default_notification_color"
93-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:19:16-84
94            android:resource="@color/notification_icon_color" />
94-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:19:85-134
95        <meta-data
95-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:20:5-132
96            android:name="expo.modules.notifications.default_notification_icon"
96-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:20:16-83
97            android:resource="@drawable/notification_icon" />
97-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:20:84-130
98        <meta-data
98-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:5-83
99            android:name="expo.modules.updates.ENABLED"
99-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:16-59
100            android:value="false" />
100-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:60-81
101        <meta-data
101-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:22:5-105
102            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
102-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:22:16-80
103            android:value="ALWAYS" />
103-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:22:81-103
104        <meta-data
104-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:23:5-99
105            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
105-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:23:16-79
106            android:value="0" />
106-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:23:80-97
107
108        <activity
108-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:5-35:16
109            android:name="com.alertcomm.app.MainActivity"
109-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:15-43
110            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
110-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:44-134
111            android:exported="true"
111-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:256-279
112            android:launchMode="singleTask"
112-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:135-166
113            android:screenOrientation="portrait"
113-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:280-316
114            android:theme="@style/Theme.App.SplashScreen"
114-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:210-255
115            android:windowSoftInputMode="adjustResize" >
115-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:167-209
116            <intent-filter>
116-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:25:7-28:23
117                <action android:name="android.intent.action.MAIN" />
117-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:26:9-60
117-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:26:17-58
118
119                <category android:name="android.intent.category.LAUNCHER" />
119-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:27:9-68
119-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:27:19-66
120            </intent-filter>
121            <intent-filter>
121-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:29:7-34:23
122                <action android:name="android.intent.action.VIEW" />
122-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:11:7-58
122-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:11:15-56
123
124                <category android:name="android.intent.category.DEFAULT" />
124-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:31:9-67
124-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:31:19-65
125                <category android:name="android.intent.category.BROWSABLE" />
125-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:12:7-67
125-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:12:17-65
126
127                <data android:scheme="exp+alertcomm-app" />
127-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:13:7-37
127-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:13:13-35
128            </intent-filter>
129        </activity>
130        <activity
130-->[:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-25:20
131            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
131-->[:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-81
132            android:exported="true"
132-->[:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-36
133            android:launchMode="singleTask"
133-->[:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-44
134            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
134-->[:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-70
135            <intent-filter>
135-->[:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-24:29
136                <action android:name="android.intent.action.VIEW" />
136-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:11:7-58
136-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:11:15-56
137
138                <category android:name="android.intent.category.DEFAULT" />
138-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:31:9-67
138-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:31:19-65
139                <category android:name="android.intent.category.BROWSABLE" />
139-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:12:7-67
139-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:12:17-65
140
141                <data android:scheme="expo-dev-launcher" />
141-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:13:7-37
141-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:13:13-35
142            </intent-filter>
143        </activity>
144        <activity
144-->[:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:9-29:70
145            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
145-->[:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-93
146            android:screenOrientation="portrait"
146-->[:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-49
147            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
147-->[:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-67
148        <activity
148-->[:expo-dev-menu] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-21:20
149            android:name="expo.modules.devmenu.DevMenuActivity"
149-->[:expo-dev-menu] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-64
150            android:exported="true"
150-->[:expo-dev-menu] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-36
151            android:launchMode="singleTask"
151-->[:expo-dev-menu] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-44
152            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
152-->[:expo-dev-menu] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-75
153            <intent-filter>
153-->[:expo-dev-menu] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-20:29
154                <action android:name="android.intent.action.VIEW" />
154-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:11:7-58
154-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:11:15-56
155
156                <category android:name="android.intent.category.DEFAULT" />
156-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:31:9-67
156-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:31:19-65
157                <category android:name="android.intent.category.BROWSABLE" />
157-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:12:7-67
157-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:12:17-65
158
159                <data android:scheme="expo-dev-menu" />
159-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:13:7-37
159-->/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:13:13-35
160            </intent-filter>
161        </activity>
162
163        <meta-data
163-->[:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-11:89
164            android:name="org.unimodules.core.AppLoader#react-native-headless"
164-->[:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-79
165            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
165-->[:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-86
166        <meta-data
166-->[:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-15:45
167            android:name="com.facebook.soloader.enabled"
167-->[:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-57
168            android:value="true" />
168-->[:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-33
169
170        <activity
170-->[com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/AndroidManifest.xml:19:9-21:40
171            android:name="com.facebook.react.devsupport.DevSettingsActivity"
171-->[com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/AndroidManifest.xml:20:13-77
172            android:exported="false" />
172-->[com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/AndroidManifest.xml:21:13-37
173
174        <provider
174-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:21:9-30:20
175            android:name="expo.modules.filesystem.FileSystemFileProvider"
175-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:22:13-74
176            android:authorities="com.alertcomm.app.FileSystemFileProvider"
176-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:23:13-74
177            android:exported="false"
177-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:24:13-37
178            android:grantUriPermissions="true" >
178-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:25:13-47
179            <meta-data
179-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:27:13-29:70
180                android:name="android.support.FILE_PROVIDER_PATHS"
180-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:28:17-67
181                android:resource="@xml/file_system_provider_paths" />
181-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:29:17-67
182        </provider>
183
184        <service
184-->[host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/4c5ca8463323586cb345100497b452b6/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:11:9-14:56
185            android:name="expo.modules.location.services.LocationTaskService"
185-->[host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/4c5ca8463323586cb345100497b452b6/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:12:13-78
186            android:exported="false"
186-->[host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/4c5ca8463323586cb345100497b452b6/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:13:13-37
187            android:foregroundServiceType="location" />
187-->[host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/4c5ca8463323586cb345100497b452b6/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:14:13-53
188        <service
188-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:11:9-17:19
189            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
189-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:12:13-91
190            android:exported="false" >
190-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:13:13-37
191            <intent-filter android:priority="-1" >
191-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:14:13-16:29
191-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:14:28-49
192                <action android:name="com.google.firebase.MESSAGING_EVENT" />
192-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:15:17-78
192-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:15:25-75
193            </intent-filter>
194        </service>
195
196        <receiver
196-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:19:9-31:20
197            android:name="expo.modules.notifications.service.NotificationsService"
197-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:20:13-83
198            android:enabled="true"
198-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:21:13-35
199            android:exported="false" >
199-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:22:13-37
200            <intent-filter android:priority="-1" >
200-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:23:13-30:29
200-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:23:28-49
201                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
201-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:24:17-88
201-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:24:25-85
202                <action android:name="android.intent.action.BOOT_COMPLETED" />
202-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:25:17-79
202-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:25:25-76
203                <action android:name="android.intent.action.REBOOT" />
203-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:26:17-71
203-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:26:25-68
204                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
204-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:27:17-82
204-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:27:25-79
205                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
205-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:28:17-82
205-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:28:25-79
206                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
206-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:29:17-84
206-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:29:25-81
207            </intent-filter>
208        </receiver>
209
210        <activity
210-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:33:9-40:75
211            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
211-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:34:13-92
212            android:excludeFromRecents="true"
212-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:35:13-46
213            android:exported="false"
213-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:36:13-37
214            android:launchMode="standard"
214-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:37:13-42
215            android:noHistory="true"
215-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:38:13-37
216            android:taskAffinity=""
216-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:39:13-36
217            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
217-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:40:13-72
218
219        <receiver
219-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:29:9-40:20
220            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
220-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:30:13-78
221            android:exported="true"
221-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:31:13-36
222            android:permission="com.google.android.c2dm.permission.SEND" >
222-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:32:13-73
223            <intent-filter>
223-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:33:13-35:29
224                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
224-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:34:17-81
224-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:34:25-78
225            </intent-filter>
226
227            <meta-data
227-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:37:13-39:40
228                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
228-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:38:17-92
229                android:value="true" />
229-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:39:17-37
230        </receiver>
231        <!--
232             FirebaseMessagingService performs security checks at runtime,
233             but set to not exported to explicitly avoid allowing another app to call it.
234        -->
235        <service
235-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:46:9-53:19
236            android:name="com.google.firebase.messaging.FirebaseMessagingService"
236-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:47:13-82
237            android:directBootAware="true"
237-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:48:13-43
238            android:exported="false" >
238-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:49:13-37
239            <intent-filter android:priority="-500" >
239-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:14:13-16:29
239-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:14:28-49
240                <action android:name="com.google.firebase.MESSAGING_EVENT" />
240-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:15:17-78
240-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:15:25-75
241            </intent-filter>
242        </service>
243        <service
243-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:54:9-63:19
244            android:name="com.google.firebase.components.ComponentDiscoveryService"
244-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:55:13-84
245            android:directBootAware="true"
245-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:32:13-43
246            android:exported="false" >
246-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:56:13-37
247            <meta-data
247-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:57:13-59:85
248                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
248-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:58:17-122
249                android:value="com.google.firebase.components.ComponentRegistrar" />
249-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:59:17-82
250            <meta-data
250-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:60:13-62:85
251                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
251-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:61:17-119
252                android:value="com.google.firebase.components.ComponentRegistrar" />
252-->[com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:62:17-82
253            <meta-data
253-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:15:13-17:85
254                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
254-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:16:17-130
255                android:value="com.google.firebase.components.ComponentRegistrar" />
255-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:17:17-82
256            <meta-data
256-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:18:13-20:85
257                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
257-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:19:17-127
258                android:value="com.google.firebase.components.ComponentRegistrar" />
258-->[com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:20:17-82
259            <meta-data
259-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9b0e9068d083797e5391f7d235389ef3/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
260                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
260-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9b0e9068d083797e5391f7d235389ef3/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
261                android:value="com.google.firebase.components.ComponentRegistrar" />
261-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9b0e9068d083797e5391f7d235389ef3/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
262            <meta-data
262-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
263                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
263-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:36:17-109
264                android:value="com.google.firebase.components.ComponentRegistrar" />
264-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:37:17-82
265            <meta-data
265-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/f26638c1f5a9cd79c0db4383d52470a9/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
266                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
266-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/f26638c1f5a9cd79c0db4383d52470a9/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
267                android:value="com.google.firebase.components.ComponentRegistrar" />
267-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/f26638c1f5a9cd79c0db4383d52470a9/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
268        </service>
269
270        <provider
270-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:29:9-37:20
271            android:name="androidx.startup.InitializationProvider"
271-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:30:13-67
272            android:authorities="com.alertcomm.app.androidx-startup"
272-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:31:13-68
273            android:exported="false" >
273-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:32:13-37
274            <meta-data
274-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:34:13-36:52
275                android:name="androidx.work.WorkManagerInitializer"
275-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:35:17-68
276                android:value="androidx.startup" />
276-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:36:17-49
277            <meta-data
277-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/6a06dd8db20fe48f1d4dcd15ee18fe12/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
278                android:name="androidx.emoji2.text.EmojiCompatInitializer"
278-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/6a06dd8db20fe48f1d4dcd15ee18fe12/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
279                android:value="androidx.startup" />
279-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/6a06dd8db20fe48f1d4dcd15ee18fe12/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
280            <meta-data
280-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b59c4c4f9e32b429eb79d019301b76c8/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
281                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
281-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b59c4c4f9e32b429eb79d019301b76c8/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
282                android:value="androidx.startup" />
282-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b59c4c4f9e32b429eb79d019301b76c8/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
283            <meta-data
283-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
284                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
284-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
285                android:value="androidx.startup" />
285-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
286        </provider>
287
288        <service
288-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:39:9-45:35
289            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
289-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:40:13-88
290            android:directBootAware="false"
290-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:41:13-44
291            android:enabled="@bool/enable_system_alarm_service_default"
291-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:42:13-72
292            android:exported="false" />
292-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:43:13-37
293        <service
293-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:46:9-52:35
294            android:name="androidx.work.impl.background.systemjob.SystemJobService"
294-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:47:13-84
295            android:directBootAware="false"
295-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:48:13-44
296            android:enabled="@bool/enable_system_job_service_default"
296-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:49:13-70
297            android:exported="true"
297-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:50:13-36
298            android:permission="android.permission.BIND_JOB_SERVICE" />
298-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:51:13-69
299        <service
299-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:53:9-59:35
300            android:name="androidx.work.impl.foreground.SystemForegroundService"
300-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:54:13-81
301            android:directBootAware="false"
301-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:55:13-44
302            android:enabled="@bool/enable_system_foreground_service_default"
302-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:56:13-77
303            android:exported="false" />
303-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:57:13-37
304
305        <receiver
305-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:61:9-66:35
306            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
306-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:62:13-88
307            android:directBootAware="false"
307-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:63:13-44
308            android:enabled="true"
308-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:64:13-35
309            android:exported="false" />
309-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:65:13-37
310        <receiver
310-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:67:9-77:20
311            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
311-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:68:13-106
312            android:directBootAware="false"
312-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:69:13-44
313            android:enabled="false"
313-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:70:13-36
314            android:exported="false" >
314-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:71:13-37
315            <intent-filter>
315-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:73:13-76:29
316                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
316-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:74:17-87
316-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:74:25-84
317                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
317-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:75:17-90
317-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:75:25-87
318            </intent-filter>
319        </receiver>
320        <receiver
320-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:78:9-88:20
321            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
321-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:79:13-104
322            android:directBootAware="false"
322-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:80:13-44
323            android:enabled="false"
323-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:81:13-36
324            android:exported="false" >
324-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:82:13-37
325            <intent-filter>
325-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:84:13-87:29
326                <action android:name="android.intent.action.BATTERY_OKAY" />
326-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:85:17-77
326-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:85:25-74
327                <action android:name="android.intent.action.BATTERY_LOW" />
327-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:86:17-76
327-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:86:25-73
328            </intent-filter>
329        </receiver>
330        <receiver
330-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:89:9-99:20
331            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
331-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:90:13-104
332            android:directBootAware="false"
332-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:91:13-44
333            android:enabled="false"
333-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:92:13-36
334            android:exported="false" >
334-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:93:13-37
335            <intent-filter>
335-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:95:13-98:29
336                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
336-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:96:17-83
336-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:96:25-80
337                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
337-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:97:17-82
337-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:97:25-79
338            </intent-filter>
339        </receiver>
340        <receiver
340-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:100:9-109:20
341            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
341-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:101:13-103
342            android:directBootAware="false"
342-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:102:13-44
343            android:enabled="false"
343-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:103:13-36
344            android:exported="false" >
344-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:104:13-37
345            <intent-filter>
345-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:106:13-108:29
346                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
346-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:107:17-79
346-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:107:25-76
347            </intent-filter>
348        </receiver>
349        <receiver
349-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:110:9-121:20
350            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
350-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:111:13-88
351            android:directBootAware="false"
351-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:112:13-44
352            android:enabled="false"
352-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:113:13-36
353            android:exported="false" >
353-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:114:13-37
354            <intent-filter>
354-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:116:13-120:29
355                <action android:name="android.intent.action.BOOT_COMPLETED" />
355-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:25:17-79
355-->[host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:25:25-76
356                <action android:name="android.intent.action.TIME_SET" />
356-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:118:17-73
356-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:118:25-70
357                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
357-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:119:17-81
357-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:119:25-78
358            </intent-filter>
359        </receiver>
360        <receiver
360-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:122:9-131:20
361            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
361-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:123:13-99
362            android:directBootAware="false"
362-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:124:13-44
363            android:enabled="@bool/enable_system_alarm_service_default"
363-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:125:13-72
364            android:exported="false" >
364-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:126:13-37
365            <intent-filter>
365-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:128:13-130:29
366                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
366-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:129:17-98
366-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:129:25-95
367            </intent-filter>
368        </receiver>
369        <receiver
369-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:132:9-142:20
370            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
370-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:133:13-78
371            android:directBootAware="false"
371-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:134:13-44
372            android:enabled="true"
372-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:135:13-35
373            android:exported="true"
373-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:136:13-36
374            android:permission="android.permission.DUMP" >
374-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:137:13-57
375            <intent-filter>
375-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:139:13-141:29
376                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
376-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:140:17-88
376-->[androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:140:25-85
377            </intent-filter>
378        </receiver>
379
380        <service
380-->[androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/61c957488e5abc44912a78441e6969b6/transformed/room-runtime-2.5.0/AndroidManifest.xml:24:9-28:63
381            android:name="androidx.room.MultiInstanceInvalidationService"
381-->[androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/61c957488e5abc44912a78441e6969b6/transformed/room-runtime-2.5.0/AndroidManifest.xml:25:13-74
382            android:directBootAware="true"
382-->[androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/61c957488e5abc44912a78441e6969b6/transformed/room-runtime-2.5.0/AndroidManifest.xml:26:13-43
383            android:exported="false" />
383-->[androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/61c957488e5abc44912a78441e6969b6/transformed/room-runtime-2.5.0/AndroidManifest.xml:27:13-37
384
385        <meta-data
385-->[com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/8a27fe877e7ae68239dd68f638887df5/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:23:9-25:69
386            android:name="com.google.android.gms.version"
386-->[com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/8a27fe877e7ae68239dd68f638887df5/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:24:13-58
387            android:value="@integer/google_play_services_version" /> <!-- Needs to be explicitly declared on P+ -->
387-->[com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/8a27fe877e7ae68239dd68f638887df5/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:25:13-66
388        <uses-library
388-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:33:7-86
389            android:name="org.apache.http.legacy"
389-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:33:21-58
390            android:required="false" />
390-->[com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:33:59-83
391
392        <activity
392-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/ca5f3ad87bb5a176fcf5402bbea57c24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:9-173
393            android:name="com.google.android.gms.common.api.GoogleApiActivity"
393-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/ca5f3ad87bb5a176fcf5402bbea57c24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:19-85
394            android:exported="false"
394-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/ca5f3ad87bb5a176fcf5402bbea57c24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:146-170
395            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
395-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/ca5f3ad87bb5a176fcf5402bbea57c24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:86-145
396
397        <provider
397-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
398            android:name="com.google.firebase.provider.FirebaseInitProvider"
398-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:24:13-77
399            android:authorities="com.alertcomm.app.firebaseinitprovider"
399-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:25:13-72
400            android:directBootAware="true"
400-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:26:13-43
401            android:exported="false"
401-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:27:13-37
402            android:initOrder="100" />
402-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:28:13-36
403
404        <receiver
404-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
405            android:name="androidx.profileinstaller.ProfileInstallReceiver"
405-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
406            android:directBootAware="false"
406-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
407            android:enabled="true"
407-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
408            android:exported="true"
408-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
409            android:permission="android.permission.DUMP" >
409-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
410            <intent-filter>
410-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
411                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
411-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
411-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
412            </intent-filter>
413            <intent-filter>
413-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
414                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
414-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
414-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
415            </intent-filter>
416            <intent-filter>
416-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
417                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
417-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
417-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
418            </intent-filter>
419            <intent-filter>
419-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
420                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
420-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
420-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
421            </intent-filter>
422        </receiver>
423
424        <service
424-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
425            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
425-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
426            android:exported="false" >
426-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
427            <meta-data
427-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
428                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
428-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
429                android:value="cct" />
429-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
430        </service>
431        <service
431-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
432            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
432-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
433            android:exported="false"
433-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
434            android:permission="android.permission.BIND_JOB_SERVICE" >
434-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
435        </service>
436
437        <receiver
437-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
438            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
438-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
439            android:exported="false" />
439-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
440    </application>
441
442</manifest>
