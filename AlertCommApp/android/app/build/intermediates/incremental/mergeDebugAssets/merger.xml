<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":expo-updates-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-updates-interface/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-json-utils" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-json-utils/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-manifests" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-manifests/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-dev-client" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-client/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-constants/android/build/intermediates/library_assets/debug/packageDebugAssets/out"><file name="app.config" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-constants/android/build/intermediates/library_assets/debug/packageDebugAssets/out/app.config"/></source></dataSet><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-permissions" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-permissions/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-dev-menu-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu-interface/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo-dev-menu" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out"><file name="Inter-Bold.otf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Bold.otf"/><file name="Inter-Regular.otf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Regular.otf"/><file name="Inter-Thin.otf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Thin.otf"/><file name="Inter-Light.otf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Light.otf"/><file name="EXDevMenuApp.android.js" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/EXDevMenuApp.android.js"/><file name="Inter-Medium.otf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Medium.otf"/><file name="Inter-SemiBold.otf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-SemiBold.otf"/><file name="dev-menu-packager-host" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/dev-menu-packager-host"/><file name="Inter-Black.otf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Black.otf"/><file name="Inter-ExtraBold.otf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-ExtraBold.otf"/><file name="Inter-ExtraLight.otf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-ExtraLight.otf"/></source></dataSet><dataSet config=":expo-dev-launcher" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out"><file name="Inter-Bold.otf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Bold.otf"/><file name="Inter-Regular.otf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Regular.otf"/><file name="Inter-Thin.otf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Thin.otf"/><file name="Inter-Light.otf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Light.otf"/><file name="expo_dev_launcher_android.bundle" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/expo_dev_launcher_android.bundle"/><file name="Inter-Medium.otf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Medium.otf"/><file name="Inter-SemiBold.otf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-SemiBold.otf"/><file name="Inter-Black.otf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-Black.otf"/><file name="Inter-ExtraBold.otf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-ExtraBold.otf"/><file name="Inter-ExtraLight.otf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/library_assets/debug/packageDebugAssets/out/Inter-ExtraLight.otf"/></source></dataSet><dataSet config=":react-native-vector-icons" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-push-notification" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-push-notification/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-picker_picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-edge-to-edge" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-maps" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config=":react-native-gesture-handler" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/intermediates/library_assets/debug/packageDebugAssets/out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets"><file name="public/favicon.ico" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/favicon.ico"/><file name="public/index.html" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/index.html"/><file name="public/cordova.js" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/cordova.js"/><file name="public/metadata.json" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/metadata.json"/><file name="public/_expo/static/js/web/index-29b6f4fec73e1da6dcd4bd7aed65be5b.js" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/_expo/static/js/web/index-29b6f4fec73e1da6dcd4bd7aed65be5b.js"/><file name="public/cordova_plugins.js" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/cordova_plugins.js"/><file name="public/assets/node_modules/@react-navigation/elements/lib/module/assets/<EMAIL>" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@react-navigation/elements/lib/module/assets/<EMAIL>"/><file name="public/assets/node_modules/@react-navigation/elements/lib/module/assets/<EMAIL>" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@react-navigation/elements/lib/module/assets/<EMAIL>"/><file name="public/assets/node_modules/@react-navigation/elements/lib/module/assets/search-icon.286d67d3f74808a60a78d3ebf1a5fb57.png" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@react-navigation/elements/lib/module/assets/search-icon.286d67d3f74808a60a78d3ebf1a5fb57.png"/><file name="public/assets/node_modules/@react-navigation/elements/lib/module/assets/<EMAIL>" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@react-navigation/elements/lib/module/assets/<EMAIL>"/><file name="public/assets/node_modules/@react-navigation/elements/lib/module/assets/<EMAIL>" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@react-navigation/elements/lib/module/assets/<EMAIL>"/><file name="public/assets/node_modules/@react-navigation/elements/lib/module/assets/<EMAIL>" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@react-navigation/elements/lib/module/assets/<EMAIL>"/><file name="public/assets/node_modules/@react-navigation/elements/lib/module/assets/close-icon.808e1b1b9b53114ec2838071a7e6daa7.png" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@react-navigation/elements/lib/module/assets/close-icon.808e1b1b9b53114ec2838071a7e6daa7.png"/><file name="public/assets/node_modules/@react-navigation/elements/lib/module/assets/<EMAIL>" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@react-navigation/elements/lib/module/assets/<EMAIL>"/><file name="public/assets/node_modules/@react-navigation/elements/lib/module/assets/back-icon.35ba0eaec5a4f5ed12ca16fabeae451d.png" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@react-navigation/elements/lib/module/assets/back-icon.35ba0eaec5a4f5ed12ca16fabeae451d.png"/><file name="public/assets/node_modules/@react-navigation/elements/lib/module/assets/back-icon-mask.0a328cd9c1afd0afe8e3b1ec5165b1b4.png" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@react-navigation/elements/lib/module/assets/back-icon-mask.0a328cd9c1afd0afe8e3b1ec5165b1b4.png"/><file name="public/assets/node_modules/@react-navigation/elements/lib/module/assets/clear-icon.c94f6478e7ae0cdd9f15de1fcb9e5e55.png" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@react-navigation/elements/lib/module/assets/clear-icon.c94f6478e7ae0cdd9f15de1fcb9e5e55.png"/><file name="public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/MaterialIcons.4e85bc9ebe07e0340c9c4fc2f6c38908.ttf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/MaterialIcons.4e85bc9ebe07e0340c9c4fc2f6c38908.ttf"/><file name="public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Foundation.e20945d7c929279ef7a6f1db184a4470.ttf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Foundation.e20945d7c929279ef7a6f1db184a4470.ttf"/><file name="public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/EvilIcons.140c53a7643ea949007aa9a282153849.ttf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/EvilIcons.140c53a7643ea949007aa9a282153849.ttf"/><file name="public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Entypo.31b5ffea3daddc69dd01a1f3d6cf63c5.ttf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Entypo.31b5ffea3daddc69dd01a1f3d6cf63c5.ttf"/><file name="public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome5_Regular.1f77739ca9ff2188b539c36f30ffa2be.ttf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome5_Regular.1f77739ca9ff2188b539c36f30ffa2be.ttf"/><file name="public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Octicons.f7c53c47a66934504fcbc7cc164895a7.ttf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Octicons.f7c53c47a66934504fcbc7cc164895a7.ttf"/><file name="public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/MaterialCommunityIcons.b62641afc9ab487008e996a5c5865e56.ttf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/MaterialCommunityIcons.b62641afc9ab487008e996a5c5865e56.ttf"/><file name="public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/SimpleLineIcons.d2285965fe34b05465047401b8595dd0.ttf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/SimpleLineIcons.d2285965fe34b05465047401b8595dd0.ttf"/><file name="public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/AntDesign.3a2ba31570920eeb9b1d217cabe58315.ttf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/AntDesign.3a2ba31570920eeb9b1d217cabe58315.ttf"/><file name="public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome6_Solid.adec7d6f310bc577f05e8fe06a5daccf.ttf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome6_Solid.adec7d6f310bc577f05e8fe06a5daccf.ttf"/><file name="public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome5_Solid.605ed7926cf39a2ad5ec2d1f9d391d3d.ttf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome5_Solid.605ed7926cf39a2ad5ec2d1f9d391d3d.ttf"/><file name="public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome6_Regular.370dd5af19f8364907b6e2c41f45dbbf.ttf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome6_Regular.370dd5af19f8364907b6e2c41f45dbbf.ttf"/><file name="public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome.b06871f281fee6b241d60582ae9369b9.ttf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome.b06871f281fee6b241d60582ae9369b9.ttf"/><file name="public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Fontisto.b49ae8ab2dbccb02c4d11caaacf09eab.ttf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Fontisto.b49ae8ab2dbccb02c4d11caaacf09eab.ttf"/><file name="public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Zocial.1681f34aaca71b8dfb70756bca331eb2.ttf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Zocial.1681f34aaca71b8dfb70756bca331eb2.ttf"/><file name="public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome6_Brands.56c8d80832e37783f12c05db7c8849e2.ttf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome6_Brands.56c8d80832e37783f12c05db7c8849e2.ttf"/><file name="public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome5_Brands.3b89dd103490708d19a95adcae52210e.ttf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome5_Brands.3b89dd103490708d19a95adcae52210e.ttf"/><file name="public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Feather.a76d309774d33d9856f650bed4292a23.ttf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Feather.a76d309774d33d9856f650bed4292a23.ttf"/><file name="public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Ionicons.6148e7019854f3bde85b633cb88f3c25.ttf" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/public/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Ionicons.6148e7019854f3bde85b633cb88f3c25.ttf"/><file name="capacitor.config.json" path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/assets/capacitor.config.json"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/shader_assets/debug/compileDebugShaders/out"/></dataSet></merger>