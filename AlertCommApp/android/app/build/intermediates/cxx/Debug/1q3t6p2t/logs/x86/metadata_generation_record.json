[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: x86", "file_": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/android_gradle_build.json due to:", "file_": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/java \\\n  --class-path \\\n  /Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.prefab/cli/2.1.0/aa32fec809c44fa531f01dcfb739b5b3304d3050/cli-2.1.0-all.jar \\\n  com.google.prefab.cli.AppKt \\\n  --build-system \\\n  cmake \\\n  --platform \\\n  android \\\n  --abi \\\n  x86 \\\n  --os-version \\\n  24 \\\n  --stl \\\n  c++_shared \\\n  --ndk-version \\\n  27 \\\n  --output \\\n  /var/folders/z_/g8r9ysb11hz32td4d16qml7w0000gn/T/agp-prefab-staging5659680781411787987/staged-cli-output \\\n  /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab \\\n  /Users/<USER>/.gradle/caches/8.13/transforms/fa03d1ffcf2d88d54ed6c7d80f87d2da/transformed/hermes-android-0.79.5-debug/prefab \\\n  /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab\n", "file_": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from '/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86'", "file_": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder '/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86'", "file_": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  -H/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=24 \\\n  -DANDROID_PLATFORM=android-24 \\\n  -DANDROID_ABI=x86 \\\n  -DCMAKE_ANDROID_ARCH_ABI=x86 \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \\\n  -DC<PERSON>KE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86 \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86 \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  -DCMAKE_FIND_ROOT_PATH=/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/prefab/x86/prefab \\\n  -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86 \\\n  -GNinja \\\n  -DPROJECT_BUILD_DIR=/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build \\\n  -DPROJECT_ROOT_DIR=/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android \\\n  -DREACT_ANDROID_DIR=/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid \\\n  -DANDROID_STL=c++_shared \\\n  -DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\n", "file_": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  -H/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=24 \\\n  -DANDROID_PLATFORM=android-24 \\\n  -DANDROID_ABI=x86 \\\n  -DCMAKE_ANDROID_ARCH_ABI=x86 \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \\\n  -DCMA<PERSON>_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86 \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86 \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  -DCMAKE_FIND_ROOT_PATH=/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/prefab/x86/prefab \\\n  -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86 \\\n  -GNinja \\\n  -DPROJECT_BUILD_DIR=/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build \\\n  -DPROJECT_ROOT_DIR=/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android \\\n  -DREACT_ANDROID_DIR=/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid \\\n  -DANDROID_STL=c++_shared \\\n  -DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\n", "file_": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/compile_commands.json.bin normally", "file_": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/compile_commands.json to /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/tools/debug/x86/compile_commands.json", "file_": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]