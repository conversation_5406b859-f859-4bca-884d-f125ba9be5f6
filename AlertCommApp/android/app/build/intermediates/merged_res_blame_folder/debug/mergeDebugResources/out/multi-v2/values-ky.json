{"logs": [{"outputFile": "com.alertcomm.app-mergeDebugResources-58:/values-ky/values-ky.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,569,673,784", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "150,252,355,462,564,668,779,880"}, "to": {"startLines": "38,39,40,41,42,43,44,145", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3505,3605,3707,3810,3917,4019,4123,13114", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "3600,3702,3805,3912,4014,4118,4229,13210"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/4bf03df1e227318d4ab7c4ce04613cbc/transformed/appcompat-1.7.0/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,426,535,647,732,837,954,1033,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2236,2341,2439,2546,2649,2764,2925,12643", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "421,530,642,727,832,949,1028,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2231,2336,2434,2541,2644,2759,2920,3022,12720"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/667e71e4345aed7ed3545c710439fc52/transformed/play-services-basement-18.4.0/res/values-ky/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "153", "endOffsets": "348"}, "to": {"startLines": "57", "startColumns": "4", "startOffsets": "5635", "endColumns": "157", "endOffsets": "5788"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/dfa8d219fb756bbf0447e8d2f088c459/transformed/browser-1.6.0/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,154,259,368", "endColumns": "98,104,108,105", "endOffsets": "149,254,363,469"}, "to": {"startLines": "67,71,72,73", "startColumns": "4,4,4,4", "startOffsets": "6852,7169,7274,7383", "endColumns": "98,104,108,105", "endOffsets": "6946,7269,7378,7484"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7a9086f53045ff51dd925c6f1785f9fd/transformed/material-1.12.0/res/values-ky/values-ky.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,348,433,518,633,743,844,985,1069,1129,1193,1287,1357,1418,1505,1568,1632,1691,1765,1827,1881,1998,2056,2117,2171,2245,2367,2451,2530,2630,2716,2812,2944,3022,3100,3229,3318,3398,3459,3514,3580,3649,3726,3797,3878,3952,4028,4118,4191,4293,4378,4457,4547,4639,4713,4798,4888,4940,5024,5089,5174,5259,5321,5385,5448,5517,5634,5742,5842,5946,6011,6070,6152,6238,6314", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,82,84,84,114,109,100,140,83,59,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,78,99,85,95,131,77,77,128,88,79,60,54,65,68,76,70,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,107,99,103,64,58,81,85,75,82", "endOffsets": "260,343,428,513,628,738,839,980,1064,1124,1188,1282,1352,1413,1500,1563,1627,1686,1760,1822,1876,1993,2051,2112,2166,2240,2362,2446,2525,2625,2711,2807,2939,3017,3095,3224,3313,3393,3454,3509,3575,3644,3721,3792,3873,3947,4023,4113,4186,4288,4373,4452,4542,4634,4708,4793,4883,4935,5019,5084,5169,5254,5316,5380,5443,5512,5629,5737,5837,5941,6006,6065,6147,6233,6309,6392"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,68,69,70,74,77,79,80,81,82,83,84,85,86,87,88,89,90,91,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3027,3110,3195,3280,3395,4234,4335,4476,6951,7011,7075,7489,7711,7842,7929,7992,8056,8115,8189,8251,8305,8422,8480,8541,8595,8669,8858,8942,9021,9121,9207,9303,9435,9513,9591,9720,9809,9889,9950,10005,10071,10140,10217,10288,10369,10443,10519,10609,10682,10784,10869,10948,11038,11130,11204,11289,11379,11431,11515,11580,11665,11750,11812,11876,11939,12008,12125,12233,12333,12437,12502,12561,12725,12811,12887", "endLines": "5,33,34,35,36,37,45,46,47,68,69,70,74,77,79,80,81,82,83,84,85,86,87,88,89,90,91,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,140,141,142", "endColumns": "12,82,84,84,114,109,100,140,83,59,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,78,99,85,95,131,77,77,128,88,79,60,54,65,68,76,70,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,107,99,103,64,58,81,85,75,82", "endOffsets": "310,3105,3190,3275,3390,3500,4330,4471,4555,7006,7070,7164,7554,7767,7924,7987,8051,8110,8184,8246,8300,8417,8475,8536,8590,8664,8786,8937,9016,9116,9202,9298,9430,9508,9586,9715,9804,9884,9945,10000,10066,10135,10212,10283,10364,10438,10514,10604,10677,10779,10864,10943,11033,11125,11199,11284,11374,11426,11510,11575,11660,11745,11807,11871,11934,12003,12120,12228,12328,12432,12497,12556,12638,12806,12882,12965"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,143,212,295,365,432,504", "endColumns": "87,68,82,69,66,71,71", "endOffsets": "138,207,290,360,427,499,571"}, "to": {"startLines": "48,75,76,78,92,143,144", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4560,7559,7628,7772,8791,12970,13042", "endColumns": "87,68,82,69,66,71,71", "endOffsets": "4643,7623,7706,7837,8853,13037,13109"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/ca5f3ad87bb5a176fcf5402bbea57c24/transformed/play-services-base-18.5.0/res/values-ky/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,440,565,668,809,932,1043,1148,1313,1416,1562,1688,1821,1981,2041,2097", "endColumns": "101,144,124,102,140,122,110,104,164,102,145,125,132,159,59,55,73", "endOffsets": "294,439,564,667,808,931,1042,1147,1312,1415,1561,1687,1820,1980,2040,2096,2170"}, "to": {"startLines": "49,50,51,52,53,54,55,56,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4648,4754,4903,5032,5139,5284,5411,5526,5793,5962,6069,6219,6349,6486,6650,6714,6774", "endColumns": "105,148,128,106,144,126,114,108,168,106,149,129,136,163,63,59,77", "endOffsets": "4749,4898,5027,5134,5279,5406,5521,5630,5957,6064,6214,6344,6481,6645,6709,6769,6847"}}]}]}