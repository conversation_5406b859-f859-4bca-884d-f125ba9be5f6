{"logs": [{"outputFile": "com.alertcomm.app-mergeDebugResources-58:/values-nb/values-nb.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/ca5f3ad87bb5a176fcf5402bbea57c24/transformed/play-services-base-18.5.0/res/values-nb/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4374,4480,4639,4765,4874,5030,5160,5280,5513,5667,5774,5935,6063,6205,6381,6448,6510", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "4475,4634,4760,4869,5025,5155,5275,5378,5662,5769,5930,6058,6200,6376,6443,6505,6583"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/667e71e4345aed7ed3545c710439fc52/transformed/play-services-basement-18.4.0/res/values-nb/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5383", "endColumns": "129", "endOffsets": "5508"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/dfa8d219fb756bbf0447e8d2f088c459/transformed/browser-1.6.0/res/values-nb/values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "66,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6588,6913,7014,7126", "endColumns": "109,100,111,96", "endOffsets": "6693,7009,7121,7218"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/res/values-nb/values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "38,39,40,41,42,43,44,138", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3348,3442,3544,3641,3740,3848,3954,12228", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3437,3539,3636,3735,3843,3949,4069,12324"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7a9086f53045ff51dd925c6f1785f9fd/transformed/material-1.12.0/res/values-nb/values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1025,1089,1181,1249,1309,1396,1460,1522,1586,1654,1719,1773,1882,1940,2002,2056,2131,2251,2333,2410,2500,2584,2664,2798,2876,2956,3079,3167,3245,3299,3350,3416,3484,3558,3629,3705,3776,3854,3924,3994,4094,4183,4261,4349,4439,4511,4583,4667,4718,4796,4862,4943,5026,5088,5152,5215,5284,5384,5488,5581,5681,5739,5794,5872,5956,6034", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "256,333,406,493,581,661,760,879,961,1020,1084,1176,1244,1304,1391,1455,1517,1581,1649,1714,1768,1877,1935,1997,2051,2126,2246,2328,2405,2495,2579,2659,2793,2871,2951,3074,3162,3240,3294,3345,3411,3479,3553,3624,3700,3771,3849,3919,3989,4089,4178,4256,4344,4434,4506,4578,4662,4713,4791,4857,4938,5021,5083,5147,5210,5279,5379,5483,5576,5676,5734,5789,5867,5951,6029,6101"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2943,3020,3093,3180,3268,4074,4173,4292,6698,6757,6821,7223,7291,7351,7438,7502,7564,7628,7696,7761,7815,7924,7982,8044,8098,8173,8293,8375,8452,8542,8626,8706,8840,8918,8998,9121,9209,9287,9341,9392,9458,9526,9600,9671,9747,9818,9896,9966,10036,10136,10225,10303,10391,10481,10553,10625,10709,10760,10838,10904,10985,11068,11130,11194,11257,11326,11426,11530,11623,11723,11781,11836,11994,12078,12156", "endLines": "5,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,136,137", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "306,3015,3088,3175,3263,3343,4168,4287,4369,6752,6816,6908,7286,7346,7433,7497,7559,7623,7691,7756,7810,7919,7977,8039,8093,8168,8288,8370,8447,8537,8621,8701,8835,8913,8993,9116,9204,9282,9336,9387,9453,9521,9595,9666,9742,9813,9891,9961,10031,10131,10220,10298,10386,10476,10548,10620,10704,10755,10833,10899,10980,11063,11125,11189,11252,11321,11421,11525,11618,11718,11776,11831,11909,12073,12151,12223"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/4bf03df1e227318d4ab7c4ce04613cbc/transformed/appcompat-1.7.0/res/values-nb/values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,414,509,623,709,809,922,999,1074,1165,1258,1352,1446,1546,1639,1734,1832,1923,2014,2092,2195,2293,2389,2493,2592,2693,2846,11914", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "409,504,618,704,804,917,994,1069,1160,1253,1347,1441,1541,1634,1729,1827,1918,2009,2087,2190,2288,2384,2488,2587,2688,2841,2938,11989"}}]}]}