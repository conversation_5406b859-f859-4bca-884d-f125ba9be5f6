{"logs": [{"outputFile": "com.alertcomm.app-mergeDebugResources-58:/values-lt/values-lt.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/667e71e4345aed7ed3545c710439fc52/transformed/play-services-basement-18.4.0/res/values-lt/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "154", "endOffsets": "349"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5831", "endColumns": "158", "endOffsets": "5985"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/res/values-lt/values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,213,286,357,444,514,582,660,742,824,905,979,1062,1146,1224,1307,1390,1466,1542,1616,1713,1788,1870,1943", "endColumns": "72,84,72,70,86,69,67,77,81,81,80,73,82,83,77,82,82,75,75,73,96,74,81,72,79", "endOffsets": "123,208,281,352,439,509,577,655,737,819,900,974,1057,1141,1219,1302,1385,1461,1537,1611,1708,1783,1865,1938,2018"}, "to": {"startLines": "35,51,77,79,80,82,96,97,98,145,146,147,148,153,154,155,156,157,158,159,160,162,163,164,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3194,4740,7749,7900,7971,8123,9199,9267,9345,13231,13313,13394,13468,13875,13959,14037,14120,14203,14279,14355,14429,14627,14702,14784,14857", "endColumns": "72,84,72,70,86,69,67,77,81,81,80,73,82,83,77,82,82,75,75,73,96,74,81,72,79", "endOffsets": "3262,4820,7817,7966,8053,8188,9262,9340,9422,13308,13389,13463,13546,13954,14032,14115,14198,14274,14350,14424,14521,14697,14779,14852,14932"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/4bf03df1e227318d4ab7c4ce04613cbc/transformed/appcompat-1.7.0/res/values-lt/values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "425,541,645,758,845,947,1069,1152,1232,1326,1422,1519,1615,1718,1814,1912,2008,2102,2196,2279,2388,2496,2596,2706,2811,2917,3093,13551", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "536,640,753,840,942,1064,1147,1227,1321,1417,1514,1610,1713,1809,1907,2003,2097,2191,2274,2383,2491,2591,2701,2806,2912,3088,3189,13630"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/ca5f3ad87bb5a176fcf5402bbea57c24/transformed/play-services-base-18.5.0/res/values-lt/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,444,572,675,824,950,1065,1167,1329,1434,1595,1725,1874,2020,2084,2146", "endColumns": "102,147,127,102,148,125,114,101,161,104,160,129,148,145,63,61,85", "endOffsets": "295,443,571,674,823,949,1064,1166,1328,1433,1594,1724,1873,2019,2083,2145,2231"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4825,4932,5084,5216,5323,5476,5606,5725,5990,6156,6265,6430,6564,6717,6867,6935,7001", "endColumns": "106,151,131,106,152,129,118,105,165,108,164,133,152,149,67,65,89", "endOffsets": "4927,5079,5211,5318,5471,5601,5720,5826,6151,6260,6425,6559,6712,6862,6930,6996,7086"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/dfa8d219fb756bbf0447e8d2f088c459/transformed/browser-1.6.0/res/values-lt/values-lt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,265,379", "endColumns": "104,104,113,105", "endOffsets": "155,260,374,480"}, "to": {"startLines": "70,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "7091,7424,7529,7643", "endColumns": "104,104,113,105", "endOffsets": "7191,7524,7638,7744"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7a9086f53045ff51dd925c6f1785f9fd/transformed/material-1.12.0/res/values-lt/values-lt.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,375,454,532,615,709,799,895,1013,1097,1160,1226,1325,1403,1468,1578,1641,1713,1772,1846,1907,1961,2085,2146,2208,2262,2340,2474,2562,2639,2732,2813,2897,3038,3117,3201,3344,3441,3518,3574,3628,3694,3769,3848,3919,3999,4075,4153,4226,4303,4410,4497,4578,4668,4760,4832,4913,5005,5060,5142,5208,5293,5380,5442,5506,5569,5641,5752,5868,5969,6078,6138,6196,6278,6364,6440", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,78,77,82,93,89,95,117,83,62,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,76,92,80,83,140,78,83,142,96,76,55,53,65,74,78,70,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81,85,75,77", "endOffsets": "370,449,527,610,704,794,890,1008,1092,1155,1221,1320,1398,1463,1573,1636,1708,1767,1841,1902,1956,2080,2141,2203,2257,2335,2469,2557,2634,2727,2808,2892,3033,3112,3196,3339,3436,3513,3569,3623,3689,3764,3843,3914,3994,4070,4148,4221,4298,4405,4492,4573,4663,4755,4827,4908,5000,5055,5137,5203,5288,5375,5437,5501,5564,5636,5747,5863,5964,6073,6133,6191,6273,6359,6435,6513"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,71,72,73,78,81,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150,151,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3267,3346,3424,3507,3601,4442,4538,4656,7196,7259,7325,7822,8058,8193,8303,8366,8438,8497,8571,8632,8686,8810,8871,8933,8987,9065,9427,9515,9592,9685,9766,9850,9991,10070,10154,10297,10394,10471,10527,10581,10647,10722,10801,10872,10952,11028,11106,11179,11256,11363,11450,11531,11621,11713,11785,11866,11958,12013,12095,12161,12246,12333,12395,12459,12522,12594,12705,12821,12922,13031,13091,13149,13635,13721,13797", "endLines": "7,36,37,38,39,40,48,49,50,71,72,73,78,81,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,150,151,152", "endColumns": "12,78,77,82,93,89,95,117,83,62,65,98,77,64,109,62,71,58,73,60,53,123,60,61,53,77,133,87,76,92,80,83,140,78,83,142,96,76,55,53,65,74,78,70,79,75,77,72,76,106,86,80,89,91,71,80,91,54,81,65,84,86,61,63,62,71,110,115,100,108,59,57,81,85,75,77", "endOffsets": "420,3341,3419,3502,3596,3686,4533,4651,4735,7254,7320,7419,7895,8118,8298,8361,8433,8492,8566,8627,8681,8805,8866,8928,8982,9060,9194,9510,9587,9680,9761,9845,9986,10065,10149,10292,10389,10466,10522,10576,10642,10717,10796,10867,10947,11023,11101,11174,11251,11358,11445,11526,11616,11708,11780,11861,11953,12008,12090,12156,12241,12328,12390,12454,12517,12589,12700,12816,12917,13026,13086,13144,13226,13716,13792,13870"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/res/values-lt/values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "41,42,43,44,45,46,47,161", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3691,3789,3899,3998,4101,4212,4322,14526", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "3784,3894,3993,4096,4207,4317,4437,14622"}}]}]}