{"logs": [{"outputFile": "com.alertcomm.app-mergeDebugResources-60:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/ca5f3ad87bb5a176fcf5402bbea57c24/transformed/play-services-base-18.5.0/res/values-pt-rPT/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,457,581,685,849,973,1091,1196,1380,1484,1650,1777,1932,2106,2170,2235", "endColumns": "100,158,123,103,163,123,117,104,183,103,165,126,154,173,63,64,82", "endOffsets": "297,456,580,684,848,972,1090,1195,1379,1483,1649,1776,1931,2105,2169,2234,2317"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4683,4788,4951,5079,5187,5355,5483,5605,5859,6047,6155,6325,6456,6615,6793,6861,6930", "endColumns": "104,162,127,107,167,127,121,108,187,107,169,130,158,177,67,68,86", "endOffsets": "4783,4946,5074,5182,5350,5478,5600,5709,6042,6150,6320,6451,6610,6788,6856,6925,7012"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/4bf03df1e227318d4ab7c4ce04613cbc/transformed/appcompat-1.7.0/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,537,644,733,834,952,1037,1117,1209,1303,1400,1494,1593,1687,1783,1878,1970,2062,2147,2254,2365,2467,2575,2683,2790,2955,13392", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "426,532,639,728,829,947,1032,1112,1204,1298,1395,1489,1588,1682,1778,1873,1965,2057,2142,2249,2360,2462,2570,2678,2785,2950,3049,13473"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,210,281,351,434,504,583,662,750,834,908,997,1081,1157,1238,1320,1395,1473,1547,1637,1709,1795,1871", "endColumns": "68,85,70,69,82,69,78,78,87,83,73,88,83,75,80,81,74,77,73,89,71,85,75,85", "endOffsets": "119,205,276,346,429,499,578,657,745,829,903,992,1076,1152,1233,1315,1390,1468,1542,1632,1704,1790,1866,1952"}, "to": {"startLines": "33,49,75,77,78,80,94,95,142,143,144,145,150,151,152,153,154,155,156,157,159,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3054,4597,7663,7813,7883,8031,9095,9174,13057,13145,13229,13303,13719,13803,13879,13960,14042,14117,14195,14269,14460,14532,14618,14694", "endColumns": "68,85,70,69,82,69,78,78,87,83,73,88,83,75,80,81,74,77,73,89,71,85,75,85", "endOffsets": "3118,4678,7729,7878,7961,8096,9169,9248,13140,13224,13298,13387,13798,13874,13955,14037,14112,14190,14264,14354,14527,14613,14689,14775"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/667e71e4345aed7ed3545c710439fc52/transformed/play-services-basement-18.4.0/res/values-pt-rPT/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5714", "endColumns": "144", "endOffsets": "5854"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "39,40,41,42,43,44,45,158", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3561,3658,3760,3859,3959,4066,4172,14359", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "3653,3755,3854,3954,4061,4167,4288,14455"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7a9086f53045ff51dd925c6f1785f9fd/transformed/material-1.12.0/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,434,516,615,711,814,934,1015,1075,1139,1231,1310,1375,1465,1529,1597,1659,1732,1796,1850,1976,2034,2096,2150,2226,2369,2456,2536,2635,2721,2803,2942,3024,3106,3242,3329,3409,3465,3516,3582,3657,3737,3808,3887,3960,4037,4106,4180,4287,4380,4457,4550,4648,4722,4803,4902,4955,5039,5105,5194,5282,5344,5408,5471,5539,5655,5763,5870,5972,6032,6087,6173,6256,6335", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,81,98,95,102,119,80,59,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,135,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85,82,78,78", "endOffsets": "268,349,429,511,610,706,809,929,1010,1070,1134,1226,1305,1370,1460,1524,1592,1654,1727,1791,1845,1971,2029,2091,2145,2221,2364,2451,2531,2630,2716,2798,2937,3019,3101,3237,3324,3404,3460,3511,3577,3652,3732,3803,3882,3955,4032,4101,4175,4282,4375,4452,4545,4643,4717,4798,4897,4950,5034,5100,5189,5277,5339,5403,5466,5534,5650,5758,5865,5967,6027,6082,6168,6251,6330,6409"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,69,70,71,76,79,81,82,83,84,85,86,87,88,89,90,91,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147,148,149", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3123,3204,3284,3366,3465,4293,4396,4516,7133,7193,7257,7734,7966,8101,8191,8255,8323,8385,8458,8522,8576,8702,8760,8822,8876,8952,9253,9340,9420,9519,9605,9687,9826,9908,9990,10126,10213,10293,10349,10400,10466,10541,10621,10692,10771,10844,10921,10990,11064,11171,11264,11341,11434,11532,11606,11687,11786,11839,11923,11989,12078,12166,12228,12292,12355,12423,12539,12647,12754,12856,12916,12971,13478,13561,13640", "endLines": "5,34,35,36,37,38,46,47,48,69,70,71,76,79,81,82,83,84,85,86,87,88,89,90,91,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,147,148,149", "endColumns": "12,80,79,81,98,95,102,119,80,59,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,135,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85,82,78,78", "endOffsets": "318,3199,3279,3361,3460,3556,4391,4511,4592,7188,7252,7344,7808,8026,8186,8250,8318,8380,8453,8517,8571,8697,8755,8817,8871,8947,9090,9335,9415,9514,9600,9682,9821,9903,9985,10121,10208,10288,10344,10395,10461,10536,10616,10687,10766,10839,10916,10985,11059,11166,11259,11336,11429,11527,11601,11682,11781,11834,11918,11984,12073,12161,12223,12287,12350,12418,12534,12642,12749,12851,12911,12966,13052,13556,13635,13714"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/dfa8d219fb756bbf0447e8d2f088c459/transformed/browser-1.6.0/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,171,270,382", "endColumns": "115,98,111,102", "endOffsets": "166,265,377,480"}, "to": {"startLines": "68,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "7017,7349,7448,7560", "endColumns": "115,98,111,102", "endOffsets": "7128,7443,7555,7658"}}]}]}