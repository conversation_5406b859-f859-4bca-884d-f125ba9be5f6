-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:1:1-37:12
MERGED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:1:1-37:12
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:react-native-gesture-handler] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-maps] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-edge-to-edge] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-async-storage_async-storage] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-picker_picker] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-push-notification] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-push-notification/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vector-icons] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-32:12
MERGED from [:expo-dev-menu] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-24:12
MERGED from [:expo-dev-menu-interface] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu-interface/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-18:12
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/0c036dd4e89c039bdb830b38729aba27/transformed/expo.modules.font-13.3.2/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/AndroidManifest.xml:2:1-24:12
MERGED from [:expo-constants] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-client] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-client/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-manifests] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-manifests/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-json-utils] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-json-utils/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:expo-updates-interface] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-updates-interface/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.application:6.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/f4396ccf453ef624d8c67ac2b15d4f34/transformed/expo.modules.application-6.1.5/AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/5e45f93c4e65d3e73e3912e20442b03d/transformed/expo.modules.asset-11.1.7/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:2:1-33:12
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/cdecb3a2f1c538aa08f216a388624fd7/transformed/expo.modules.keepawake-14.1.4/AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/4c5ca8463323586cb345100497b452b6/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:2:1-17:12
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:2:1-43:12
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7a9086f53045ff51dd925c6f1785f9fd/transformed/material-1.12.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9b0e9068d083797e5391f7d235389ef3/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:2:1-18:12
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:17:1-145:12
MERGED from [androidx.room:room-ktx:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/e82a40cf8f8f2afb94477070470e0b58/transformed/room-ktx-2.5.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/61c957488e5abc44912a78441e6969b6/transformed/room-runtime-2.5.0/AndroidManifest.xml:17:1-31:12
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/b12c76904e43659374d0daea05575fca/transformed/play-services-location-21.3.0/AndroidManifest.xml:2:1-8:12
MERGED from [com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/8a27fe877e7ae68239dd68f638887df5/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:16:1-35:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/ca5f3ad87bb5a176fcf5402bbea57c24/transformed/play-services-base-18.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/cafe38d429a8d27ed8b0af5abbc941f2/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/1b766223c9ad7f2daee8b81f58bc7213/transformed/constraintlayout-2.0.1/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/4bf03df1e227318d4ab7c4ce04613cbc/transformed/appcompat-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/8770a77af4f549302c0ccc4f3429b863/transformed/lifecycle-extensions-2.2.0/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/081581c012a4283b28daac0fe17aa771/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/a2fc949d6d75cb7f96da1072a54dd82a/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3afca45618f18dc581937d02b2bdc40f/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/6f6c5f039a38b430a1949a1708dda5dc/transformed/firebase-installations-interop-17.1.1/AndroidManifest.xml:15:1-19:12
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/a4309c375f7c655b9a04946660dc90cc/transformed/activity-1.8.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/0778a2aa5b019b2def3eb71c0ed4a154/transformed/activity-ktx-1.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/829cfe53c64cfc693496f8451f29e7a8/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f9db95877b6b5303346dab89be2bb74e/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/28bc247258e5cff6e6aac54078e1de43/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/a177d82bc21b186c301bc9b6c8d7387d/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/b52e52fc777190b4295f59d0a858517f/transformed/autofill-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:animated-gif:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/04b5e14ace161c416b3792cc65073136/transformed/animated-gif-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/a1e7fd93c38007b66fe1ce6dffa1cb17/transformed/webpsupport-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/d67b401b98683845e20d868c9079182a/transformed/fresco-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce52cb4064562b4b9386643be0fc7867/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/2bf1d0ccdaf978e74a899b15f35a1ba1/transformed/animated-base-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/2330eff14f5a13aa56b81f00e6eedfcf/transformed/animated-drawable-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/ff300a4baf9f1bf29410ae898edb5295/transformed/vito-options-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/6fcecc4c6bb8b8753bdcf3c3459c4fbc/transformed/drawee-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3148937ce24975b0c8bd8c282baed048/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/cd82b284ddc0eb890197a89ff6aa47b0/transformed/memory-type-native-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/4569d0f6272c114cc561a4707d109554/transformed/memory-type-java-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7c448edad99bc61105557ce32ab74a1b/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/b5b6dc9338af08dcda60bafb8897c85b/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/85dde86bea35887bac0feb7d53cce7ae/transformed/imagepipeline-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e6d6d9b25fc67e5dd2f7142d2fad5488/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7c2022e539491c06e7973071945ca586/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7e93b6b7e7c43855a5d878759065fa91/transformed/urimod-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3a4438f452b91905d3d5ba17982c1aa1/transformed/vito-source-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/8cfee973423e06e528b5d8479c9cb2e9/transformed/middleware-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/9a88919693188ddbdd185f7e98648316/transformed/ui-common-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/59b487b2b096ccdefac5e140ae48e3f7/transformed/soloader-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/979a76771b5c0b857ab3556be228bba8/transformed/fbcore-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/dfa8d219fb756bbf0447e8d2f088c459/transformed/browser-1.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/72a94180d096fd79574b78e6407bc370/transformed/core-ktx-1.15.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/c2aaf2f0c5250c0e148ab60ada39c9da/transformed/transition-1.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/183e370a152c2b8981bc4a82353880f3/transformed/drawerlayout-1.1.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/a907cfc2c979bc8caf6a7799f50a94a8/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba621659a277237e85638838dad2dcb6/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/72ec46a88e05357e6517408e9569c65b/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/6bc00ab5eaca23c3c3ebd49ed8511ff9/transformed/viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/51166ce65ea68e12719e2bca48ab2cfd/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/6a06dd8db20fe48f1d4dcd15ee18fe12/transformed/emoji2-1.3.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/e2740c2d15f5f0e1c1cc73d5e9e0b8e0/transformed/media-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.13/transforms/427513327c43f4b46023352ed3ecf832/transformed/play-services-stats-17.0.2/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/66b1db28f9866d87d063c78805394f08/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/810c6942ffe8913950057d661b122986/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/618758d8ab77f01a5d167c4fd9df1af4/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/59364def5c2f903fdc1dceeb75244942/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0554aba29ce76817e6a5be45213e21ee/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/714600962765d5961827fa3521ced293/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/cbf6d65a0d97e898f7eca44154f963b3/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/850320aa2b4b52ced6f3465503644aaa/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/58726736d2c8b2cb18170698e2d2af0c/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b153c5f07cea60c340fe199e7ef9098d/transformed/lifecycle-service-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/de145cd6b45e8b1fb7e804d6c000ef76/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b59c4c4f9e32b429eb79d019301b76c8/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/7d4851cc4faabf79f6af50acd560b500/transformed/lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/7c99fc93f13e5345aee29eaa313ee725/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/86cd36f2af6251908c1cd91569dccf65/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/09f7c3694aac112487e9f730f6c374d4/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/0a40c827f1ab6cae60b0a38160d029dc/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/fcc1cc582935f0fa4fa7ad13b3b8686c/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/6c6a1a82e45a7434ecf0c3d775347d44/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:2:1-5:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7cc008949652dbb80e471afb44bf7cf0/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/667e71e4345aed7ed3545c710439fc52/transformed/play-services-basement-18.4.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/27ed71059cba4dfa5c0c363f337b269f/transformed/fragment-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/bd599e307399f0c0086eaf5c5077a9e1/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/1c233a1110e41e54547ee5b84513eb96/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7ab8f7120f36931c3468a226fbeffc5/transformed/tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/18cdabc45926f1dc3dff94376b7201d0/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/7d1e55601ba19bcfcdbfa4a02130ac50/transformed/sqlite-framework-2.3.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3d5fcfb1e81a9a7a0ab85df90f606618/transformed/ui-core-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/9b4397416aa634d6c66b07fe5b0df812/transformed/sqlite-2.3.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/6cd01ffc08af05631b09907077e7d319/transformed/vito-renderer-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/fa03d1ffcf2d88d54ed6c7d80f87d2da/transformed/hermes-android-0.79.5-debug/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.8.2] /Users/<USER>/.gradle/caches/8.13/transforms/733dbf0845ebde69fe3f06409df9221b/transformed/viewbinding-8.8.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0bffdab30bd5a7c7e1564164c0aeaa47/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/2ba03b814d30daf0b666892538f8bffe/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/cc6eb0cec1c9f801500b232161e7a948/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d5424d0a62b576aa1dd6954661a7a105/transformed/firebase-components-18.0.0/AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/f26638c1f5a9cd79c0db4383d52470a9/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/376bc31ec5340584b4d0896635c1e053/transformed/firebase-encoders-json-18.0.0/AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/c9717af2fa7f7619c5d79a69172a6cee/transformed/transport-api-3.1.0/AndroidManifest.xml:15:1-20:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/af3d4771b467d110290eceabbbcbdf9b/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5cb2f9a25605a6be165659396486475a/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/a4cb90f99ef5e2ab73584d25744c9ba2/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/0e46f645dab24076e9faaca7bbf3eaa1/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/274a5e4c52765583c47c315e8a011727/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.13/transforms/c72240e70788902de7eb47bfe9d21a7b/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.multidex:multidex:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/5811c19182af824aa48e3900cbc8382e/transformed/multidex-2.0.1/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:appcompat-v7:27.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/0fb6aadda9e6da50ceeb51b26b8280d9/transformed/appcompat-v7-27.1.1/AndroidManifest.xml:17:1-22:12
MERGED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:2:1-52:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/a99a6f26d36648368ec505176d6c6ea3/transformed/soloader-0.12.1/AndroidManifest.xml:2:1-17:12
MERGED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/c606388d75cd1d2425bd93631a9c5065/transformed/installreferrer-2.2/AndroidManifest.xml:2:1-13:12
MERGED from [com.android.support:animated-vector-drawable:27.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/ec6b8149aebd2a474f1ce6e9fa345aa7/transformed/animated-vector-drawable-27.1.1/AndroidManifest.xml:17:1-22:12
MERGED from [com.android.support:support-vector-drawable:27.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/41c9a2c2b1925b3040c78dffb29f3715/transformed/support-vector-drawable-27.1.1/AndroidManifest.xml:17:1-22:12
	package
		INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:2:3-78
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/4c5ca8463323586cb345100497b452b6/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:7:5-81
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/4c5ca8463323586cb345100497b452b6/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:7:5-81
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:2:20-76
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:3:3-76
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/4c5ca8463323586cb345100497b452b6/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:8:5-79
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/4c5ca8463323586cb345100497b452b6/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:8:5-79
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:3:20-74
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:4:3-64
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:8:5-67
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:21:5-66
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:21:5-66
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3afca45618f18dc581937d02b2bdc40f/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3afca45618f18dc581937d02b2bdc40f/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:25:5-67
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:4:20-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:5:3-77
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:10:5-80
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:10:5-80
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:5:20-75
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:6:3-75
MERGED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:6:3-75
MERGED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:6:3-75
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/AndroidManifest.xml:16:5-78
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:6:20-73
uses-permission#android.permission.VIBRATE
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:7:3-63
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:7:20-61
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:8:3-78
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:9:5-81
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:9:5-81
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:8:20-76
queries
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:9:3-15:13
MERGED from [:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-9:15
MERGED from [:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-9:15
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:12:5-18:15
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:12:5-18:15
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:27:5-30:15
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:27:5-30:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:10:5-14:14
action#android.intent.action.VIEW
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:11:7-58
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:11:15-56
category#android.intent.category.BROWSABLE
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:12:7-67
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:12:17-65
data
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:13:7-37
	android:scheme
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:13:13-35
application
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:3-36:17
MERGED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:3-36:17
MERGED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:3-36:17
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:6:5-162
MERGED from [:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:5-30:19
MERGED from [:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:5-30:19
MERGED from [:expo-dev-menu] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-22:19
MERGED from [:expo-dev-menu] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-22:19
MERGED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-16:19
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/AndroidManifest.xml:18:5-22:19
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:20:5-31:19
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:20:5-31:19
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/4c5ca8463323586cb345100497b452b6/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:10:5-15:19
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/4c5ca8463323586cb345100497b452b6/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:10:5-15:19
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:10:5-41:19
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:10:5-41:19
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7a9086f53045ff51dd925c6f1785f9fd/transformed/material-1.12.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7a9086f53045ff51dd925c6f1785f9fd/transformed/material-1.12.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9b0e9068d083797e5391f7d235389ef3/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9b0e9068d083797e5391f7d235389ef3/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:8:5-16:19
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:28:5-143:19
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/61c957488e5abc44912a78441e6969b6/transformed/room-runtime-2.5.0/AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/61c957488e5abc44912a78441e6969b6/transformed/room-runtime-2.5.0/AndroidManifest.xml:23:5-29:19
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/b12c76904e43659374d0daea05575fca/transformed/play-services-location-21.3.0/AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/b12c76904e43659374d0daea05575fca/transformed/play-services-location-21.3.0/AndroidManifest.xml:6:5-20
MERGED from [com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/8a27fe877e7ae68239dd68f638887df5/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:22:5-26:19
MERGED from [com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/8a27fe877e7ae68239dd68f638887df5/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:31:5-34:19
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:31:5-34:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/ca5f3ad87bb5a176fcf5402bbea57c24/transformed/play-services-base-18.5.0/AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/ca5f3ad87bb5a176fcf5402bbea57c24/transformed/play-services-base-18.5.0/AndroidManifest.xml:4:5-6:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/1b766223c9ad7f2daee8b81f58bc7213/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/1b766223c9ad7f2daee8b81f58bc7213/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/8770a77af4f549302c0ccc4f3429b863/transformed/lifecycle-extensions-2.2.0/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/8770a77af4f549302c0ccc4f3429b863/transformed/lifecycle-extensions-2.2.0/AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/a2fc949d6d75cb7f96da1072a54dd82a/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/a2fc949d6d75cb7f96da1072a54dd82a/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:7:5-8:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/6a06dd8db20fe48f1d4dcd15ee18fe12/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/6a06dd8db20fe48f1d4dcd15ee18fe12/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.13/transforms/427513327c43f4b46023352ed3ecf832/transformed/play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.13/transforms/427513327c43f4b46023352ed3ecf832/transformed/play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b59c4c4f9e32b429eb79d019301b76c8/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b59c4c4f9e32b429eb79d019301b76c8/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/6c6a1a82e45a7434ecf0c3d775347d44/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/6c6a1a82e45a7434ecf0c3d775347d44/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7cc008949652dbb80e471afb44bf7cf0/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7cc008949652dbb80e471afb44bf7cf0/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/667e71e4345aed7ed3545c710439fc52/transformed/play-services-basement-18.4.0/AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/667e71e4345aed7ed3545c710439fc52/transformed/play-services-basement-18.4.0/AndroidManifest.xml:5:5-7:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/1c233a1110e41e54547ee5b84513eb96/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/1c233a1110e41e54547ee5b84513eb96/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/f26638c1f5a9cd79c0db4383d52470a9/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/f26638c1f5a9cd79c0db4383d52470a9/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:25:5-39:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/0e46f645dab24076e9faaca7bbf3eaa1/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/0e46f645dab24076e9faaca7bbf3eaa1/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/a99a6f26d36648368ec505176d6c6ea3/transformed/soloader-0.12.1/AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/a99a6f26d36648368ec505176d6c6ea3/transformed/soloader-0.12.1/AndroidManifest.xml:11:5-15:19
MERGED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/c606388d75cd1d2425bd93631a9c5065/transformed/installreferrer-2.2/AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/c606388d75cd1d2425bd93631a9c5065/transformed/installreferrer-2.2/AndroidManifest.xml:11:5-20
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:221-247
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:221-247
	android:label
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:48-80
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:48-80
	tools:ignore
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:116-161
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:116-161
	tools:targetApi
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:6:54-74
	android:icon
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:81-115
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:81-115
	android:allowBackup
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:162-188
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:162-188
	android:theme
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:189-220
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:189-220
	tools:replace
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml:6:18-53
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:16-47
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:16:16-47
meta-data#com.google.firebase.messaging.default_notification_color
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:17:5-139
	android:resource
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:17:88-137
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:17:16-87
meta-data#com.google.firebase.messaging.default_notification_icon
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:18:5-135
	android:resource
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:18:87-133
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:18:16-86
meta-data#expo.modules.notifications.default_notification_color
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:19:5-136
	android:resource
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:19:85-134
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:19:16-84
meta-data#expo.modules.notifications.default_notification_icon
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:20:5-132
	android:resource
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:20:84-130
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:20:16-83
meta-data#expo.modules.updates.ENABLED
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:5-83
	android:value
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:60-81
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:21:16-59
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:22:5-105
	android:value
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:22:81-103
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:22:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:23:5-99
	android:value
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:23:80-97
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:23:16-79
activity#com.alertcomm.app.MainActivity
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:5-35:16
	android:screenOrientation
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:280-316
	android:launchMode
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:135-166
	android:windowSoftInputMode
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:167-209
	android:exported
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:256-279
	android:configChanges
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:44-134
	android:theme
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:210-255
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:24:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:25:7-28:23
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:26:9-60
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:26:17-58
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:27:9-68
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:27:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:exp+alertcomm-app
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:29:7-34:23
category#android.intent.category.DEFAULT
ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:31:9-67
	android:name
		ADDED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/main/AndroidManifest.xml:31:19-65
uses-sdk
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml
MERGED from [:react-native-gesture-handler] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-picker_picker] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-picker_picker] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-push-notification] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-push-notification/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-push-notification] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-push-notification/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu-interface/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu-interface/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/0c036dd4e89c039bdb830b38729aba27/transformed/expo.modules.font-13.3.2/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] /Users/<USER>/.gradle/caches/8.13/transforms/0c036dd4e89c039bdb830b38729aba27/transformed/expo.modules.font-13.3.2/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/AndroidManifest.xml:10:5-44
MERGED from [:expo-constants] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-constants/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-client/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-client/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-manifests/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-manifests/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-json-utils/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-json-utils/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-updates-interface/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-updates-interface/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.application:6.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/f4396ccf453ef624d8c67ac2b15d4f34/transformed/expo.modules.application-6.1.5/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.application:6.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/f4396ccf453ef624d8c67ac2b15d4f34/transformed/expo.modules.application-6.1.5/AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/5e45f93c4e65d3e73e3912e20442b03d/transformed/expo.modules.asset-11.1.7/AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/5e45f93c4e65d3e73e3912e20442b03d/transformed/expo.modules.asset-11.1.7/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/cdecb3a2f1c538aa08f216a388624fd7/transformed/expo.modules.keepawake-14.1.4/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/cdecb3a2f1c538aa08f216a388624fd7/transformed/expo.modules.keepawake-14.1.4/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/4c5ca8463323586cb345100497b452b6/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/4c5ca8463323586cb345100497b452b6/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7a9086f53045ff51dd925c6f1785f9fd/transformed/material-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] /Users/<USER>/.gradle/caches/8.13/transforms/7a9086f53045ff51dd925c6f1785f9fd/transformed/material-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9b0e9068d083797e5391f7d235389ef3/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9b0e9068d083797e5391f7d235389ef3/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/e82a40cf8f8f2afb94477070470e0b58/transformed/room-ktx-2.5.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/e82a40cf8f8f2afb94477070470e0b58/transformed/room-ktx-2.5.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/61c957488e5abc44912a78441e6969b6/transformed/room-runtime-2.5.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/61c957488e5abc44912a78441e6969b6/transformed/room-runtime-2.5.0/AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/b12c76904e43659374d0daea05575fca/transformed/play-services-location-21.3.0/AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/b12c76904e43659374d0daea05575fca/transformed/play-services-location-21.3.0/AndroidManifest.xml:4:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/8a27fe877e7ae68239dd68f638887df5/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/8a27fe877e7ae68239dd68f638887df5/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:26:5-43
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:26:5-43
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/ca5f3ad87bb5a176fcf5402bbea57c24/transformed/play-services-base-18.5.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/ca5f3ad87bb5a176fcf5402bbea57c24/transformed/play-services-base-18.5.0/AndroidManifest.xml:3:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/cafe38d429a8d27ed8b0af5abbc941f2/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/cafe38d429a8d27ed8b0af5abbc941f2/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/1b766223c9ad7f2daee8b81f58bc7213/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/1b766223c9ad7f2daee8b81f58bc7213/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/4bf03df1e227318d4ab7c4ce04613cbc/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/4bf03df1e227318d4ab7c4ce04613cbc/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/8770a77af4f549302c0ccc4f3429b863/transformed/lifecycle-extensions-2.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/8770a77af4f549302c0ccc4f3429b863/transformed/lifecycle-extensions-2.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/081581c012a4283b28daac0fe17aa771/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/081581c012a4283b28daac0fe17aa771/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/a2fc949d6d75cb7f96da1072a54dd82a/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/a2fc949d6d75cb7f96da1072a54dd82a/transformed/firebase-iid-interop-17.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3afca45618f18dc581937d02b2bdc40f/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3afca45618f18dc581937d02b2bdc40f/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/6f6c5f039a38b430a1949a1708dda5dc/transformed/firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/6f6c5f039a38b430a1949a1708dda5dc/transformed/firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/a4309c375f7c655b9a04946660dc90cc/transformed/activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/a4309c375f7c655b9a04946660dc90cc/transformed/activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/0778a2aa5b019b2def3eb71c0ed4a154/transformed/activity-ktx-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] /Users/<USER>/.gradle/caches/8.13/transforms/0778a2aa5b019b2def3eb71c0ed4a154/transformed/activity-ktx-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/829cfe53c64cfc693496f8451f29e7a8/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/829cfe53c64cfc693496f8451f29e7a8/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f9db95877b6b5303346dab89be2bb74e/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/f9db95877b6b5303346dab89be2bb74e/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/28bc247258e5cff6e6aac54078e1de43/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/28bc247258e5cff6e6aac54078e1de43/transformed/coordinatorlayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/a177d82bc21b186c301bc9b6c8d7387d/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/a177d82bc21b186c301bc9b6c8d7387d/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/b52e52fc777190b4295f59d0a858517f/transformed/autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/b52e52fc777190b4295f59d0a858517f/transformed/autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:animated-gif:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/04b5e14ace161c416b3792cc65073136/transformed/animated-gif-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/04b5e14ace161c416b3792cc65073136/transformed/animated-gif-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/a1e7fd93c38007b66fe1ce6dffa1cb17/transformed/webpsupport-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/a1e7fd93c38007b66fe1ce6dffa1cb17/transformed/webpsupport-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/d67b401b98683845e20d868c9079182a/transformed/fresco-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/d67b401b98683845e20d868c9079182a/transformed/fresco-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce52cb4064562b4b9386643be0fc7867/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/ce52cb4064562b4b9386643be0fc7867/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/2bf1d0ccdaf978e74a899b15f35a1ba1/transformed/animated-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/2bf1d0ccdaf978e74a899b15f35a1ba1/transformed/animated-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/2330eff14f5a13aa56b81f00e6eedfcf/transformed/animated-drawable-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/2330eff14f5a13aa56b81f00e6eedfcf/transformed/animated-drawable-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/ff300a4baf9f1bf29410ae898edb5295/transformed/vito-options-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/ff300a4baf9f1bf29410ae898edb5295/transformed/vito-options-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/6fcecc4c6bb8b8753bdcf3c3459c4fbc/transformed/drawee-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/6fcecc4c6bb8b8753bdcf3c3459c4fbc/transformed/drawee-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3148937ce24975b0c8bd8c282baed048/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3148937ce24975b0c8bd8c282baed048/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/cd82b284ddc0eb890197a89ff6aa47b0/transformed/memory-type-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/cd82b284ddc0eb890197a89ff6aa47b0/transformed/memory-type-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/4569d0f6272c114cc561a4707d109554/transformed/memory-type-java-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/4569d0f6272c114cc561a4707d109554/transformed/memory-type-java-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7c448edad99bc61105557ce32ab74a1b/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7c448edad99bc61105557ce32ab74a1b/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/b5b6dc9338af08dcda60bafb8897c85b/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/b5b6dc9338af08dcda60bafb8897c85b/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/85dde86bea35887bac0feb7d53cce7ae/transformed/imagepipeline-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/85dde86bea35887bac0feb7d53cce7ae/transformed/imagepipeline-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e6d6d9b25fc67e5dd2f7142d2fad5488/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/e6d6d9b25fc67e5dd2f7142d2fad5488/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7c2022e539491c06e7973071945ca586/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7c2022e539491c06e7973071945ca586/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7e93b6b7e7c43855a5d878759065fa91/transformed/urimod-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/7e93b6b7e7c43855a5d878759065fa91/transformed/urimod-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3a4438f452b91905d3d5ba17982c1aa1/transformed/vito-source-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3a4438f452b91905d3d5ba17982c1aa1/transformed/vito-source-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/8cfee973423e06e528b5d8479c9cb2e9/transformed/middleware-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/8cfee973423e06e528b5d8479c9cb2e9/transformed/middleware-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/9a88919693188ddbdd185f7e98648316/transformed/ui-common-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/9a88919693188ddbdd185f7e98648316/transformed/ui-common-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/59b487b2b096ccdefac5e140ae48e3f7/transformed/soloader-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/59b487b2b096ccdefac5e140ae48e3f7/transformed/soloader-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/979a76771b5c0b857ab3556be228bba8/transformed/fbcore-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/979a76771b5c0b857ab3556be228bba8/transformed/fbcore-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/dfa8d219fb756bbf0447e8d2f088c459/transformed/browser-1.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/dfa8d219fb756bbf0447e8d2f088c459/transformed/browser-1.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/72a94180d096fd79574b78e6407bc370/transformed/core-ktx-1.15.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/72a94180d096fd79574b78e6407bc370/transformed/core-ktx-1.15.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/c2aaf2f0c5250c0e148ab60ada39c9da/transformed/transition-1.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/c2aaf2f0c5250c0e148ab60ada39c9da/transformed/transition-1.5.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/183e370a152c2b8981bc4a82353880f3/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/183e370a152c2b8981bc4a82353880f3/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/a907cfc2c979bc8caf6a7799f50a94a8/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/a907cfc2c979bc8caf6a7799f50a94a8/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba621659a277237e85638838dad2dcb6/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/ba621659a277237e85638838dad2dcb6/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/72ec46a88e05357e6517408e9569c65b/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/72ec46a88e05357e6517408e9569c65b/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/6bc00ab5eaca23c3c3ebd49ed8511ff9/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/6bc00ab5eaca23c3c3ebd49ed8511ff9/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/51166ce65ea68e12719e2bca48ab2cfd/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/51166ce65ea68e12719e2bca48ab2cfd/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/6a06dd8db20fe48f1d4dcd15ee18fe12/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/6a06dd8db20fe48f1d4dcd15ee18fe12/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/e2740c2d15f5f0e1c1cc73d5e9e0b8e0/transformed/media-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/e2740c2d15f5f0e1c1cc73d5e9e0b8e0/transformed/media-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.13/transforms/427513327c43f4b46023352ed3ecf832/transformed/play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.13/transforms/427513327c43f4b46023352ed3ecf832/transformed/play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/66b1db28f9866d87d063c78805394f08/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/66b1db28f9866d87d063c78805394f08/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/810c6942ffe8913950057d661b122986/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/810c6942ffe8913950057d661b122986/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/618758d8ab77f01a5d167c4fd9df1af4/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/618758d8ab77f01a5d167c4fd9df1af4/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/59364def5c2f903fdc1dceeb75244942/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/59364def5c2f903fdc1dceeb75244942/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0554aba29ce76817e6a5be45213e21ee/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0554aba29ce76817e6a5be45213e21ee/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/714600962765d5961827fa3521ced293/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/714600962765d5961827fa3521ced293/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/cbf6d65a0d97e898f7eca44154f963b3/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/cbf6d65a0d97e898f7eca44154f963b3/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/850320aa2b4b52ced6f3465503644aaa/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/850320aa2b4b52ced6f3465503644aaa/transformed/lifecycle-viewmodel-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/58726736d2c8b2cb18170698e2d2af0c/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/58726736d2c8b2cb18170698e2d2af0c/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b153c5f07cea60c340fe199e7ef9098d/transformed/lifecycle-service-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b153c5f07cea60c340fe199e7ef9098d/transformed/lifecycle-service-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/de145cd6b45e8b1fb7e804d6c000ef76/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/de145cd6b45e8b1fb7e804d6c000ef76/transformed/lifecycle-runtime-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b59c4c4f9e32b429eb79d019301b76c8/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b59c4c4f9e32b429eb79d019301b76c8/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/7d4851cc4faabf79f6af50acd560b500/transformed/lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/7d4851cc4faabf79f6af50acd560b500/transformed/lifecycle-livedata-core-ktx-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/7c99fc93f13e5345aee29eaa313ee725/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/7c99fc93f13e5345aee29eaa313ee725/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/86cd36f2af6251908c1cd91569dccf65/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/86cd36f2af6251908c1cd91569dccf65/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/09f7c3694aac112487e9f730f6c374d4/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/09f7c3694aac112487e9f730f6c374d4/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/0a40c827f1ab6cae60b0a38160d029dc/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/0a40c827f1ab6cae60b0a38160d029dc/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/fcc1cc582935f0fa4fa7ad13b3b8686c/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.13/transforms/fcc1cc582935f0fa4fa7ad13b3b8686c/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/6c6a1a82e45a7434ecf0c3d775347d44/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/6c6a1a82e45a7434ecf0c3d775347d44/transformed/play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7cc008949652dbb80e471afb44bf7cf0/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/7cc008949652dbb80e471afb44bf7cf0/transformed/firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/667e71e4345aed7ed3545c710439fc52/transformed/play-services-basement-18.4.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/667e71e4345aed7ed3545c710439fc52/transformed/play-services-basement-18.4.0/AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/27ed71059cba4dfa5c0c363f337b269f/transformed/fragment-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/27ed71059cba4dfa5c0c363f337b269f/transformed/fragment-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/bd599e307399f0c0086eaf5c5077a9e1/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] /Users/<USER>/.gradle/caches/8.13/transforms/bd599e307399f0c0086eaf5c5077a9e1/transformed/fragment-ktx-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/1c233a1110e41e54547ee5b84513eb96/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/1c233a1110e41e54547ee5b84513eb96/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7ab8f7120f36931c3468a226fbeffc5/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/f7ab8f7120f36931c3468a226fbeffc5/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/18cdabc45926f1dc3dff94376b7201d0/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/18cdabc45926f1dc3dff94376b7201d0/transformed/tracing-ktx-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/7d1e55601ba19bcfcdbfa4a02130ac50/transformed/sqlite-framework-2.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/7d1e55601ba19bcfcdbfa4a02130ac50/transformed/sqlite-framework-2.3.0/AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3d5fcfb1e81a9a7a0ab85df90f606618/transformed/ui-core-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/3d5fcfb1e81a9a7a0ab85df90f606618/transformed/ui-core-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/9b4397416aa634d6c66b07fe5b0df812/transformed/sqlite-2.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/9b4397416aa634d6c66b07fe5b0df812/transformed/sqlite-2.3.0/AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/6cd01ffc08af05631b09907077e7d319/transformed/vito-renderer-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/6cd01ffc08af05631b09907077e7d319/transformed/vito-renderer-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/fa03d1ffcf2d88d54ed6c7d80f87d2da/transformed/hermes-android-0.79.5-debug/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/fa03d1ffcf2d88d54ed6c7d80f87d2da/transformed/hermes-android-0.79.5-debug/AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] /Users/<USER>/.gradle/caches/8.13/transforms/733dbf0845ebde69fe3f06409df9221b/transformed/viewbinding-8.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] /Users/<USER>/.gradle/caches/8.13/transforms/733dbf0845ebde69fe3f06409df9221b/transformed/viewbinding-8.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0bffdab30bd5a7c7e1564164c0aeaa47/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/0bffdab30bd5a7c7e1564164c0aeaa47/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/2ba03b814d30daf0b666892538f8bffe/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/2ba03b814d30daf0b666892538f8bffe/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/cc6eb0cec1c9f801500b232161e7a948/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/cc6eb0cec1c9f801500b232161e7a948/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d5424d0a62b576aa1dd6954661a7a105/transformed/firebase-components-18.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/d5424d0a62b576aa1dd6954661a7a105/transformed/firebase-components-18.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/f26638c1f5a9cd79c0db4383d52470a9/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/f26638c1f5a9cd79c0db4383d52470a9/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/376bc31ec5340584b4d0896635c1e053/transformed/firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/376bc31ec5340584b4d0896635c1e053/transformed/firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/c9717af2fa7f7619c5d79a69172a6cee/transformed/transport-api-3.1.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/c9717af2fa7f7619c5d79a69172a6cee/transformed/transport-api-3.1.0/AndroidManifest.xml:18:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/af3d4771b467d110290eceabbbcbdf9b/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/af3d4771b467d110290eceabbbcbdf9b/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5cb2f9a25605a6be165659396486475a/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/5cb2f9a25605a6be165659396486475a/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/a4cb90f99ef5e2ab73584d25744c9ba2/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/a4cb90f99ef5e2ab73584d25744c9ba2/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/0e46f645dab24076e9faaca7bbf3eaa1/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/0e46f645dab24076e9faaca7bbf3eaa1/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/274a5e4c52765583c47c315e8a011727/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/274a5e4c52765583c47c315e8a011727/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.13/transforms/c72240e70788902de7eb47bfe9d21a7b/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.13/transforms/c72240e70788902de7eb47bfe9d21a7b/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.multidex:multidex:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/5811c19182af824aa48e3900cbc8382e/transformed/multidex-2.0.1/AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/5811c19182af824aa48e3900cbc8382e/transformed/multidex-2.0.1/AndroidManifest.xml:20:5-43
MERGED from [com.android.support:appcompat-v7:27.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/0fb6aadda9e6da50ceeb51b26b8280d9/transformed/appcompat-v7-27.1.1/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:appcompat-v7:27.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/0fb6aadda9e6da50ceeb51b26b8280d9/transformed/appcompat-v7-27.1.1/AndroidManifest.xml:20:5-44
MERGED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/a99a6f26d36648368ec505176d6c6ea3/transformed/soloader-0.12.1/AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/a99a6f26d36648368ec505176d6c6ea3/transformed/soloader-0.12.1/AndroidManifest.xml:7:5-9:41
MERGED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/c606388d75cd1d2425bd93631a9c5065/transformed/installreferrer-2.2/AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/c606388d75cd1d2425bd93631a9c5065/transformed/installreferrer-2.2/AndroidManifest.xml:5:5-7:41
MERGED from [com.android.support:animated-vector-drawable:27.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/ec6b8149aebd2a474f1ce6e9fa345aa7/transformed/animated-vector-drawable-27.1.1/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:animated-vector-drawable:27.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/ec6b8149aebd2a474f1ce6e9fa345aa7/transformed/animated-vector-drawable-27.1.1/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-vector-drawable:27.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/41c9a2c2b1925b3040c78dffb29f3715/transformed/support-vector-drawable-27.1.1/AndroidManifest.xml:20:5-44
MERGED from [com.android.support:support-vector-drawable:27.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/41c9a2c2b1925b3040c78dffb29f3715/transformed/support-vector-drawable-27.1.1/AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/src/debug/AndroidManifest.xml
package#host.exp.exponent
ADDED from [:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-53
	android:name
		ADDED from [:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:18-50
activity#expo.modules.devlauncher.launcher.DevLauncherActivity
ADDED from [:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-25:20
	android:launchMode
		ADDED from [:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-44
	android:exported
		ADDED from [:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-36
	android:theme
		ADDED from [:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-70
	android:name
		ADDED from [:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-81
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:expo-dev-launcher
ADDED from [:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-24:29
activity#expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity
ADDED from [:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:9-29:70
	android:screenOrientation
		ADDED from [:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-49
	android:theme
		ADDED from [:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-67
	android:name
		ADDED from [:expo-dev-launcher] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-launcher/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-93
activity#expo.modules.devmenu.DevMenuActivity
ADDED from [:expo-dev-menu] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-21:20
	android:launchMode
		ADDED from [:expo-dev-menu] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-44
	android:exported
		ADDED from [:expo-dev-menu] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-36
	android:theme
		ADDED from [:expo-dev-menu] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-75
	android:name
		ADDED from [:expo-dev-menu] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-64
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:expo-dev-menu
ADDED from [:expo-dev-menu] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-dev-menu/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-20:29
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/a99a6f26d36648368ec505176d6c6ea3/transformed/soloader-0.12.1/AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/a99a6f26d36648368ec505176d6c6ea3/transformed/soloader-0.12.1/AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.13/transforms/a99a6f26d36648368ec505176d6c6ea3/transformed/soloader-0.12.1/AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-57
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.79.5] /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/AndroidManifest.xml:20:13-77
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:16:13-79
	android:name
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:26:13-48
	android:name
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:22:13-74
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:27:13-29:70
	android:resource
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:29:17-67
	android:name
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/8b4cf25cb545af971136b0f929fcdac0/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:28:17-67
service#expo.modules.location.services.LocationTaskService
ADDED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/4c5ca8463323586cb345100497b452b6/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:11:9-14:56
	android:exported
		ADDED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/4c5ca8463323586cb345100497b452b6/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:13:13-37
	android:foregroundServiceType
		ADDED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/4c5ca8463323586cb345100497b452b6/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:14:13-53
	android:name
		ADDED from [host.exp.exponent:expo.modules.location:18.1.6] /Users/<USER>/.gradle/caches/8.13/transforms/4c5ca8463323586cb345100497b452b6/transformed/expo.modules.location-18.1.6/AndroidManifest.xml:12:13-78
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:7:5-81
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:25:5-81
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:7:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:8:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:23:5-77
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:8:22-74
service#expo.modules.notifications.service.ExpoFirebaseMessagingService
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:11:9-17:19
	android:exported
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:13:13-37
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:12:13-91
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:14:13-16:29
	android:priority
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:14:28-49
action#com.google.firebase.MESSAGING_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:15:17-78
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:15:25-75
receiver#expo.modules.notifications.service.NotificationsService
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:19:9-31:20
	android:enabled
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:21:13-35
	android:exported
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:22:13-37
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:20:13-83
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:android.intent.action.REBOOT+action:name:com.htc.intent.action.QUICKBOOT_POWERON+action:name:expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:23:13-30:29
	android:priority
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:23:28-49
action#expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:24:17-88
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:24:25-85
action#android.intent.action.BOOT_COMPLETED
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:25:17-79
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:25:25-76
action#android.intent.action.REBOOT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:26:17-71
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:26:25-68
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:27:17-82
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:27:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:28:17-82
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:28:25-79
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:29:17-84
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:29:25-81
activity#expo.modules.notifications.service.NotificationForwarderActivity
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:33:9-40:75
	android:excludeFromRecents
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:35:13-46
	android:launchMode
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:37:13-42
	android:noHistory
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:38:13-37
	android:exported
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:36:13-37
	android:theme
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:40:13-72
	android:taskAffinity
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:39:13-36
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.4] /Users/<USER>/.gradle/caches/8.13/transforms/321b593aaeabf796f733de5a90da579d/transformed/expo.modules.notifications-0.31.4/AndroidManifest.xml:34:13-92
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:20:5-78
MERGED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:20:5-78
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3afca45618f18dc581937d02b2bdc40f/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3afca45618f18dc581937d02b2bdc40f/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:22:5-79
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:22:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:24:5-68
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:23:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3afca45618f18dc581937d02b2bdc40f/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3afca45618f18dc581937d02b2bdc40f/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:9:5-68
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:24:22-65
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3afca45618f18dc581937d02b2bdc40f/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/3afca45618f18dc581937d02b2bdc40f/transformed/play-services-cloud-messaging-17.2.0/AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9b0e9068d083797e5391f7d235389ef3/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9b0e9068d083797e5391f7d235389ef3/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/f26638c1f5a9cd79c0db4383d52470a9/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/f26638c1f5a9cd79c0db4383d52470a9/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] /Users/<USER>/.gradle/caches/8.13/transforms/8afee9e7e07808ffb0e5ff41410d2807/transformed/firebase-messaging-24.0.1/AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/253e5188189eb42fb42519a40648537a/transformed/firebase-installations-17.2.0/AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9b0e9068d083797e5391f7d235389ef3/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9b0e9068d083797e5391f7d235389ef3/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/9b0e9068d083797e5391f7d235389ef3/transformed/firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:26:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/6a06dd8db20fe48f1d4dcd15ee18fe12/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/6a06dd8db20fe48f1d4dcd15ee18fe12/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b59c4c4f9e32b429eb79d019301b76c8/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b59c4c4f9e32b429eb79d019301b76c8/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/1c233a1110e41e54547ee5b84513eb96/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.13/transforms/1c233a1110e41e54547ee5b84513eb96/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] /Users/<USER>/.gradle/caches/8.13/transforms/ca53054dc2f3da627dcc124181987c7e/transformed/work-runtime-2.9.1/AndroidManifest.xml:140:25-85
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/61c957488e5abc44912a78441e6969b6/transformed/room-runtime-2.5.0/AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/61c957488e5abc44912a78441e6969b6/transformed/room-runtime-2.5.0/AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/61c957488e5abc44912a78441e6969b6/transformed/room-runtime-2.5.0/AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/61c957488e5abc44912a78441e6969b6/transformed/room-runtime-2.5.0/AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/61c957488e5abc44912a78441e6969b6/transformed/room-runtime-2.5.0/AndroidManifest.xml:25:13-74
meta-data#com.google.android.gms.version
ADDED from [com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/8a27fe877e7ae68239dd68f638887df5/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:23:9-25:69
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/667e71e4345aed7ed3545c710439fc52/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/667e71e4345aed7ed3545c710439fc52/transformed/play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/8a27fe877e7ae68239dd68f638887df5/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:25:13-66
	android:name
		ADDED from [com.google.maps.android:android-maps-utils:3.10.0] /Users/<USER>/.gradle/caches/8.13/transforms/8a27fe877e7ae68239dd68f638887df5/transformed/android-maps-utils-3.10.0/AndroidManifest.xml:24:13-58
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:22:5-24:33
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:23:8-40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:24:8-31
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:29:7-61
	android:name
		ADDED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:29:16-59
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:33:7-86
	android:required
		ADDED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:33:59-83
	android:name
		ADDED from [com.google.android.gms:play-services-maps:19.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/5885b46583f883a7113a9056b42c784d/transformed/play-services-maps-19.1.0/AndroidManifest.xml:33:21-58
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/ca5f3ad87bb5a176fcf5402bbea57c24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/ca5f3ad87bb5a176fcf5402bbea57c24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/ca5f3ad87bb5a176fcf5402bbea57c24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.13/transforms/ca5f3ad87bb5a176fcf5402bbea57c24/transformed/play-services-base-18.5.0/AndroidManifest.xml:5:19-85
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.13/transforms/b51203f001dd8a68e302f739d7aeb6aa/transformed/firebase-common-21.0.0/AndroidManifest.xml:36:17-109
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/6a06dd8db20fe48f1d4dcd15ee18fe12/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/6a06dd8db20fe48f1d4dcd15ee18fe12/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/6a06dd8db20fe48f1d4dcd15ee18fe12/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:23:9-81
permission#com.alertcomm.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:26:22-94
uses-permission#com.alertcomm.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] /Users/<USER>/.gradle/caches/8.13/transforms/7f6e2422a7a3df38b96e806f5f74c2f2/transformed/core-1.15.0/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b59c4c4f9e32b429eb79d019301b76c8/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b59c4c4f9e32b429eb79d019301b76c8/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/b59c4c4f9e32b429eb79d019301b76c8/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.13/transforms/00803497dde122c5c17921be9ade43a6/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/f26638c1f5a9cd79c0db4383d52470a9/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/f26638c1f5a9cd79c0db4383d52470a9/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.13/transforms/f26638c1f5a9cd79c0db4383d52470a9/transformed/firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/6d4dbd989ed5f6d378bdd1da52311067/transformed/transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.13/transforms/d35f65d1bb0d9212046059db55d50ed5/transformed/transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:5-86
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:19:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:5-87
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:20:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:5-81
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:23:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:24:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:27:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:28:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:5-84
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:31:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:34:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:5-91
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:37:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:38:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:5-93
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:39:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:5-73
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:42:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:5-82
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:45:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:46:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:49:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:5-89
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] /Users/<USER>/.gradle/caches/8.13/transforms/7141883094dddd98d25731170e05b482/transformed/ShortcutBadger-1.1.22/AndroidManifest.xml:50:22-86
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/c606388d75cd1d2425bd93631a9c5065/transformed/installreferrer-2.2/AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:2.2] /Users/<USER>/.gradle/caches/8.13/transforms/c606388d75cd1d2425bd93631a9c5065/transformed/installreferrer-2.2/AndroidManifest.xml:9:22-107
