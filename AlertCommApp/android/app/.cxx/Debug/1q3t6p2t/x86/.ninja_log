# ninja log v5
0	18	0	/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/CMakeFiles/cmake.verify_globs	7db65421ce1f427b
9	1950	1752767890640878470	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o	1e7403950258306c
14	1979	1752767890670777688	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/States.cpp.o	517a3ecdf8e5f5fd
6	2183	1752767890869137183	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o	cb9a461d0fc83c5d
4	2635	1752767891324886326	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o	5155ba99d8ef6382
18	2703	1752767891388816251	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o	9321b2271e1e81f3
7	2771	1752767891457284462	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o	8b3781fcea0dc462
11	2801	1752767891488205217	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o	7afd0518c91e6c54
4	3007	1752767891695119390	rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o	3e777c98098dd1e1
3	3279	1752767891966537320	CMakeFiles/appmodules.dir/OnLoad.cpp.o	b1a6914a2dfc2763
21	3816	1752767892503402055	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerShadowNode.cpp.o	8ddb31ba4dbbb4fa
20	4170	1752767892852842026	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerMeasurementsManager.cpp.o	6c0bf799f07563be
1955	4306	1752767892995187303	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerState.cpp.o	a1aec83a399addbb
2635	4957	1752767893646316663	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerState.cpp.o	218ab50481a7ae31
2801	5192	1752767893878708762	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp.o	7477611fe7339118
1980	5473	1752767894151556043	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerMeasurementsManager.cpp.o	92d2f3dc3407b454
2183	5692	1752767894375417551	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerShadowNode.cpp.o	b2df59cef7e02f96
3816	5791	1752767894481071939	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp.o	2d7fa504322c7b69
4306	5916	1752767894605975383	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o	3336cbbe80da5bd8
3280	6143	1752767894828291893	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp.o	facdf84cf796b7c9
2772	6415	1752767895093585083	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp.o	2a4d07b51b39b81a
3007	6508	1752767895183154279	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp.o	70c242e9875fc2b2
2703	6591	1752767895278343508	rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/rnpicker.cpp.o	50511c8fb3c410bc
6591	6771	1752767895447574874	/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.so	1e23f443c7cffc3
5692	7075	1752767895765246512	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o	cfd56828fa0e92ec
5192	7516	1752767896203849408	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o	98b164d36fd7e0e1
5473	7532	1752767896222471836	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o	caa11c8341f03cba
4170	7548	1752767896215713306	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o	aa6f7f1d28ce62e4
5791	7858	1752767896545834192	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o	9452b01535b0ed02
4958	7961	1752767896636410088	rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o	cb23a20137f0b635
5916	8149	1752767896834088978	RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/RNMapsSpecs-generated.cpp.o	c4ba9feb251374c6
6771	8731	1752767897419389257	RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/RNMapsSpecsJSI-generated.cpp.o	e03f4b4c397e2c54
7516	9030	1752767897718363838	RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/States.cpp.o	ecc954874a694e00
7075	9883	1752767898570411911	RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/ShadowNodes.cpp.o	aafb29dbe405c590
7548	10162	1752767898849734341	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o	1e9c8f1b24169cf3
8153	10173	1752767898862544027	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o	4f9b55934764d6e8
7858	10539	1752767899225245296	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o	47fa9b1deb9f3bb
9030	10566	1752767899253833411	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o	71f59abdff044359
7533	10848	1752767899518976182	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o	659b225bc9f63445
6509	10955	1752767899608847770	RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/Props.cpp.o	5a379fffd9f38673
7961	11958	1752767900635120754	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o	f602a30d48dce669
8731	12279	1752767900964424473	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o	5d19f8f4f0544c91
9884	12406	1752767901094435975	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o	3120851f49b4b42b
6415	12724	1752767901351195316	RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/EventEmitters.cpp.o	5f382fe66acddb81
10162	12863	1752767901551240557	safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o	7841d4a75b77703c
12864	12947	1752767901626728412	/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.so	3a49442f37744c19
10173	13075	1752767901764014690	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o	3b6e901f31a32eb5
10955	13474	1752767902163172457	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o	4469138cf5a5d21
10540	13634	1752767902321385812	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o	54d02602cd057545
10566	13797	1752767902484552407	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o	3d06b117b73f64a2
6144	13827	1752767902419333285	RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/ComponentDescriptors.cpp.o	1057f112a9560d16
10848	14089	1752767902776686165	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o	327be4222ce0baaa
12279	14295	1752767902984141798	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o	1c3da056fa858a5f
12406	14369	1752767903056412069	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o	7f102ca0c14cad87
11958	14696	1752767903383185326	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o	6157ef6f741d1735
13075	15035	1752767903719088731	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o	ada42b2836d1919a
13827	15138	1752767903828814177	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o	b63a0ec6f6f14738
2	15256	1752767903744539263	CMakeFiles/appmodules.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o	36df26450f597d58
12725	15499	1752767904187921609	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o	93f65cf3cc83ad
14089	15596	1752767904286285157	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o	7469767574584b53
13797	16096	1752767904783918047	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o	268fa357c200a6df
14295	16373	1752767905060389140	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o	c50e7cda0be2adf2
14696	16428	1752767905118890517	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o	ac729e9914210c53
15256	16499	1752767905191448489	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o	44359cd28f6b8371
14369	16595	1752767905282417422	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o	2a93fed908576c3f
13634	16842	1752767905518318926	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o	cf2224cd69984ba0
15035	16920	1752767905606828864	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o	91f1d857e085891c
15138	17085	1752767905774418512	RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o	dc8e5a2aae429eac
13475	17132	1752767905806194570	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o	6884ef3f843c3eec
15499	17316	1752767906003619695	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o	ed48f8e8388a731
16096	17396	1752767906086748600	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o	d58761d5665cc3b4
15596	17467	1752767906156608450	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o	f55a5e69fe3046ca
16499	17528	1752767906219993398	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o	5b4a0f885123c3f
12947	17556	1752767906216206531	rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o	4ab01dd00f19279d
17556	17630	1752767906306773503	/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.so	c5343753aeef9b2d
16373	17705	1752767906394284441	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o	e61fe8440512aff9
16429	17775	1752767906464629082	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o	523c9367b25544e9
16595	17829	1752767906520543503	RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o	d98d49a3435bc5da
17829	17964	1752767906617042963	/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libappmodules.so	6cc096edc99cba37
