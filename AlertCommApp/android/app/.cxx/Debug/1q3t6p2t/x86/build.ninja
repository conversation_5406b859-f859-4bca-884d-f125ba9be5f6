# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: appmodules
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/
# =============================================================================
# Object build statements for SHARED_LIBRARY target appmodules


#############################################
# Order-only phony target for appmodules

build cmake_object_order_depends_target_appmodules: phony || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge cmake_object_order_depends_target_react_codegen_RNMapsSpecs cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec cmake_object_order_depends_target_react_codegen_rnasyncstorage cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen cmake_object_order_depends_target_react_codegen_rnpicker cmake_object_order_depends_target_react_codegen_rnscreens cmake_object_order_depends_target_react_codegen_safeareacontext

build CMakeFiles/appmodules.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o: CXX_COMPILER__appmodules_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles/appmodules.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/autolinking/src/main/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/react/renderer/components/RNMapsSpecs -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles/appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles/appmodules.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/autolinking/src/main/jni
  TARGET_COMPILE_PDB = CMakeFiles/appmodules.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libappmodules.pdb

build CMakeFiles/appmodules.dir/OnLoad.cpp.o: CXX_COMPILER__appmodules_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles/appmodules.dir/OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/autolinking/src/main/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/react/renderer/components/RNMapsSpecs -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles/appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles/appmodules.dir
  TARGET_COMPILE_PDB = CMakeFiles/appmodules.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libappmodules.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target appmodules


#############################################
# Link the shared library /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libappmodules.so

build /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libappmodules.so: CXX_SHARED_LIBRARY_LINKER__appmodules_Debug rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/RNMapsSpecs-generated.cpp.o RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/ComponentDescriptors.cpp.o RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/EventEmitters.cpp.o RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/Props.cpp.o RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/RNMapsSpecsJSI-generated.cpp.o RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/ShadowNodes.cpp.o RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/States.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o CMakeFiles/appmodules.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o CMakeFiles/appmodules.dir/OnLoad.cpp.o | /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.so /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.so /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.so /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.x86/libjsi.so /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.x86/libreactnative.so || /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.so /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.so /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.so RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge RNMapsSpecs_autolinked_build/react_codegen_RNMapsSpecs RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.so  /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.so  /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.so  /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so  /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.x86/libjsi.so  /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.x86/libreactnative.so  -latomic -lm
  OBJECT_DIR = CMakeFiles/appmodules.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libappmodules.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles/appmodules.dir/
  TARGET_FILE = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libappmodules.so
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libappmodules.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86 && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86 && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnasyncstorage


#############################################
# Order-only phony target for react_codegen_rnasyncstorage

build cmake_object_order_depends_target_react_codegen_rnasyncstorage: phony || rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rnasyncstorage

build rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/rnasyncstorage_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build rnasyncstorage_autolinked_build/edit_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/rnasyncstorage_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnasyncstorage_autolinked_build/rebuild_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnpicker


#############################################
# Order-only phony target for react_codegen_rnpicker

build cmake_object_order_depends_target_react_codegen_rnpicker: phony || rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerMeasurementsManager.cpp.o: CXX_COMPILER__react_codegen_rnpicker_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDialogPickerMeasurementsManager.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerMeasurementsManager.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  TARGET_COMPILE_PDB = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.pdb

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnpicker_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDialogPickerShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  TARGET_COMPILE_PDB = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.pdb

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerState.cpp.o: CXX_COMPILER__react_codegen_rnpicker_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDialogPickerState.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  TARGET_COMPILE_PDB = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.pdb

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerMeasurementsManager.cpp.o: CXX_COMPILER__react_codegen_rnpicker_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDropdownPickerMeasurementsManager.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerMeasurementsManager.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  TARGET_COMPILE_PDB = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.pdb

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnpicker_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDropdownPickerShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  TARGET_COMPILE_PDB = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.pdb

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerState.cpp.o: CXX_COMPILER__react_codegen_rnpicker_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/RNCAndroidDropdownPickerState.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  TARGET_COMPILE_PDB = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.pdb

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/rnpicker.cpp.o: CXX_COMPILER__react_codegen_rnpicker_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/rnpicker.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/rnpicker.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  TARGET_COMPILE_PDB = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.pdb

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnpicker_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker
  TARGET_COMPILE_PDB = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.pdb

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnpicker_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker
  TARGET_COMPILE_PDB = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.pdb

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp.o: CXX_COMPILER__react_codegen_rnpicker_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker
  TARGET_COMPILE_PDB = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.pdb

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnpicker_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker
  TARGET_COMPILE_PDB = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.pdb

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/States.cpp.o: CXX_COMPILER__react_codegen_rnpicker_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/States.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker
  TARGET_COMPILE_PDB = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.pdb

build rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnpicker_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnpicker
  DEFINES = -Dreact_codegen_rnpicker_EXPORTS
  DEP_FILE = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnpicker -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  OBJECT_FILE_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker
  TARGET_COMPILE_PDB = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnpicker


#############################################
# Link the shared library /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.so

build /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnpicker_Debug rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerMeasurementsManager.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerShadowNode.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDialogPickerState.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerMeasurementsManager.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerShadowNode.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/RNCAndroidDropdownPickerState.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/rnpicker.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/ComponentDescriptors.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/EventEmitters.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/Props.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/ShadowNodes.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/States.cpp.o rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/build/generated/source/codegen/jni/react/renderer/components/rnpicker/rnpickerJSI-generated.cpp.o | /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.x86/libreactnative.so /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.x86/libjsi.so /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.x86/libreactnative.so  /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.x86/libjsi.so  /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libreact_codegen_rnpicker.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = rnpicker_autolinked_build/CMakeFiles/react_codegen_rnpicker.dir/
  TARGET_FILE = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.so
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.pdb


#############################################
# Utility command for edit_cache

build rnpicker_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/rnpicker_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build rnpicker_autolinked_build/edit_cache: phony rnpicker_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnpicker_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/rnpicker_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnpicker_autolinked_build/rebuild_cache: phony rnpicker_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rngesturehandler_codegen


#############################################
# Order-only phony target for react_codegen_rngesturehandler_codegen

build cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen: phony || rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o


#############################################
# Utility command for edit_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/rngesturehandler_codegen_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build rngesturehandler_codegen_autolinked_build/edit_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/rngesturehandler_codegen_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rngesturehandler_codegen_autolinked_build/rebuild_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNMapsSpecs


#############################################
# Order-only phony target for react_codegen_RNMapsSpecs

build cmake_object_order_depends_target_react_codegen_RNMapsSpecs: phony || RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir

build RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/RNMapsSpecs-generated.cpp.o: CXX_COMPILER__react_codegen_RNMapsSpecs_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/RNMapsSpecs-generated.cpp || cmake_object_order_depends_target_react_codegen_RNMapsSpecs
  DEP_FILE = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/RNMapsSpecs-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/react/renderer/components/RNMapsSpecs -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir
  OBJECT_FILE_DIR = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir
  TARGET_COMPILE_PDB = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/
  TARGET_PDB = ""

build RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNMapsSpecs_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/react/renderer/components/RNMapsSpecs/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNMapsSpecs
  DEP_FILE = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/react/renderer/components/RNMapsSpecs -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir
  OBJECT_FILE_DIR = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs
  TARGET_COMPILE_PDB = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/
  TARGET_PDB = ""

build RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNMapsSpecs_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/react/renderer/components/RNMapsSpecs/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNMapsSpecs
  DEP_FILE = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/react/renderer/components/RNMapsSpecs -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir
  OBJECT_FILE_DIR = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs
  TARGET_COMPILE_PDB = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/
  TARGET_PDB = ""

build RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/Props.cpp.o: CXX_COMPILER__react_codegen_RNMapsSpecs_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/react/renderer/components/RNMapsSpecs/Props.cpp || cmake_object_order_depends_target_react_codegen_RNMapsSpecs
  DEP_FILE = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/react/renderer/components/RNMapsSpecs -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir
  OBJECT_FILE_DIR = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs
  TARGET_COMPILE_PDB = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/
  TARGET_PDB = ""

build RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/RNMapsSpecsJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNMapsSpecs_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/react/renderer/components/RNMapsSpecs/RNMapsSpecsJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNMapsSpecs
  DEP_FILE = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/RNMapsSpecsJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/react/renderer/components/RNMapsSpecs -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir
  OBJECT_FILE_DIR = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs
  TARGET_COMPILE_PDB = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/
  TARGET_PDB = ""

build RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNMapsSpecs_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/react/renderer/components/RNMapsSpecs/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNMapsSpecs
  DEP_FILE = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/react/renderer/components/RNMapsSpecs -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir
  OBJECT_FILE_DIR = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs
  TARGET_COMPILE_PDB = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/
  TARGET_PDB = ""

build RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/States.cpp.o: CXX_COMPILER__react_codegen_RNMapsSpecs_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/react/renderer/components/RNMapsSpecs/States.cpp || cmake_object_order_depends_target_react_codegen_RNMapsSpecs
  DEP_FILE = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/react/renderer/components/RNMapsSpecs -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir
  OBJECT_FILE_DIR = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs
  TARGET_COMPILE_PDB = RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/
  TARGET_PDB = ""



#############################################
# Object library react_codegen_RNMapsSpecs

build RNMapsSpecs_autolinked_build/react_codegen_RNMapsSpecs: phony RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/RNMapsSpecs-generated.cpp.o RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/ComponentDescriptors.cpp.o RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/EventEmitters.cpp.o RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/Props.cpp.o RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/RNMapsSpecsJSI-generated.cpp.o RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/ShadowNodes.cpp.o RNMapsSpecs_autolinked_build/CMakeFiles/react_codegen_RNMapsSpecs.dir/react/renderer/components/RNMapsSpecs/States.cpp.o


#############################################
# Utility command for edit_cache

build RNMapsSpecs_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/RNMapsSpecs_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build RNMapsSpecs_autolinked_build/edit_cache: phony RNMapsSpecs_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNMapsSpecs_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/RNMapsSpecs_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNMapsSpecs_autolinked_build/rebuild_cache: phony RNMapsSpecs_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Order-only phony target for react_codegen_safeareacontext

build cmake_object_order_depends_target_react_codegen_safeareacontext: phony || safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Link the shared library /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.so

build /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_safeareacontext_Debug safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o | /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.x86/libjsi.so /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.x86/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so  /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.x86/libjsi.so  /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.x86/libreactnative.so  -latomic -lm
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libreact_codegen_safeareacontext.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_FILE = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.so
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.pdb


#############################################
# Utility command for edit_cache

build safeareacontext_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/safeareacontext_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build safeareacontext_autolinked_build/edit_cache: phony safeareacontext_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/safeareacontext_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build safeareacontext_autolinked_build/rebuild_cache: phony safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Order-only phony target for react_codegen_rnscreens

build cmake_object_order_depends_target_react_codegen_rnscreens: phony || rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Link the shared library /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.so

build /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnscreens_Debug rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o | /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.x86/libreactnative.so /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.x86/libjsi.so /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.x86/libreactnative.so  /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.x86/libjsi.so  /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libreact_codegen_rnscreens.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_FILE = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.so
  TARGET_PDB = /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.pdb


#############################################
# Utility command for edit_cache

build rnscreens_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/rnscreens_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build rnscreens_autolinked_build/edit_cache: phony rnscreens_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/rnscreens_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnscreens_autolinked_build/rebuild_cache: phony rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNVectorIconsSpec


#############################################
# Order-only phony target for react_codegen_RNVectorIconsSpec

build cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec: phony || RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/RNVectorIconsSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/
  TARGET_PDB = ""



#############################################
# Object library react_codegen_RNVectorIconsSpec

build RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec: phony RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNVectorIconsSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/RNVectorIconsSpec_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build RNVectorIconsSpec_autolinked_build/edit_cache: phony RNVectorIconsSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNVectorIconsSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/RNVectorIconsSpec_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNVectorIconsSpec_autolinked_build/rebuild_cache: phony RNVectorIconsSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNEdgeToEdge


#############################################
# Order-only phony target for react_codegen_RNEdgeToEdge

build cmake_object_order_depends_target_react_codegen_RNEdgeToEdge: phony || RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/RNEdgeToEdge-generated.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/Props.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/States.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""



#############################################
# Object library react_codegen_RNEdgeToEdge

build RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge: phony RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o


#############################################
# Utility command for edit_cache

build RNEdgeToEdge_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/RNEdgeToEdge_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build RNEdgeToEdge_autolinked_build/edit_cache: phony RNEdgeToEdge_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNEdgeToEdge_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/RNEdgeToEdge_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNEdgeToEdge_autolinked_build/rebuild_cache: phony RNEdgeToEdge_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build appmodules: phony /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libappmodules.so

build libappmodules.so: phony /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libappmodules.so

build libreact_codegen_rnpicker.so: phony /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.so

build libreact_codegen_rnscreens.so: phony /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.so

build libreact_codegen_safeareacontext.so: phony /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.so

build react_codegen_RNEdgeToEdge: phony RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge

build react_codegen_RNMapsSpecs: phony RNMapsSpecs_autolinked_build/react_codegen_RNMapsSpecs

build react_codegen_RNVectorIconsSpec: phony RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec

build react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

build react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

build react_codegen_rnpicker: phony /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.so

build react_codegen_rnscreens: phony /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.so

build react_codegen_safeareacontext: phony /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86

build all: phony /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libappmodules.so rnasyncstorage_autolinked_build/all rnpicker_autolinked_build/all rngesturehandler_codegen_autolinked_build/all RNMapsSpecs_autolinked_build/all safeareacontext_autolinked_build/all rnscreens_autolinked_build/all RNVectorIconsSpec_autolinked_build/all RNEdgeToEdge_autolinked_build/all

# =============================================================================

#############################################
# Folder: /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/RNEdgeToEdge_autolinked_build

build RNEdgeToEdge_autolinked_build/all: phony RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge

# =============================================================================

#############################################
# Folder: /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/RNMapsSpecs_autolinked_build

build RNMapsSpecs_autolinked_build/all: phony RNMapsSpecs_autolinked_build/react_codegen_RNMapsSpecs

# =============================================================================

#############################################
# Folder: /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/RNVectorIconsSpec_autolinked_build

build RNVectorIconsSpec_autolinked_build/all: phony RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec

# =============================================================================

#############################################
# Folder: /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/rnasyncstorage_autolinked_build

build rnasyncstorage_autolinked_build/all: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

# =============================================================================

#############################################
# Folder: /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/rngesturehandler_codegen_autolinked_build

build rngesturehandler_codegen_autolinked_build/all: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

# =============================================================================

#############################################
# Folder: /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/rnpicker_autolinked_build

build rnpicker_autolinked_build/all: phony /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnpicker.so

# =============================================================================

#############################################
# Folder: /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/rnscreens_autolinked_build

build rnscreens_autolinked_build/all: phony /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_rnscreens.so

# =============================================================================

#############################################
# Folder: /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/safeareacontext_autolinked_build

build safeareacontext_autolinked_build/all: phony /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/intermediates/cxx/Debug/1q3t6p2t/obj/x86/libreact_codegen_safeareacontext.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/CMakeFiles/cmake.verify_globs | /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/abis.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/flags.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/platforms.cmake /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfig.cmake /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfigVersion.cmake /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/CMakeFiles/VerifyGlobs.cmake /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/CMakeLists.txt /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/abis.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/flags.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/platforms.cmake /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfig.cmake /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfigVersion.cmake /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/.cxx/Debug/1q3t6p2t/x86/CMakeFiles/VerifyGlobs.cmake /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-maps/android/src/main/jni/CMakeLists.txt /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt /Users/<USER>/Projects/react-native/Alertcomm/AlertCommApp/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
